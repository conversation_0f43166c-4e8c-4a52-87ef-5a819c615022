import Constants from 'expo-constants';
import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useRef, useState } from 'react';
import { ActivityIndicator, BackHandler, Linking, Platform, StyleSheet, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';
import { AuthScreen } from '../components/auth/AuthScreen';
import { AuthProvider, useAuth } from '../contexts/AuthContext';




// Configure notification handler
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

function handleRegistrationError(errorMessage: string) {
  console.error(errorMessage);
  throw new Error(errorMessage);
}

async function registerForPushNotificationsAsync() {
  if (Platform.OS === 'android') {
    Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  if (Device.isDevice) {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }
    if (finalStatus !== 'granted') {
      handleRegistrationError('Permission not granted to get push token for push notification!');
      return;
    }
    const projectId = Constants?.expoConfig?.extra?.eas?.projectId ?? Constants?.easConfig?.projectId;

    if (!projectId) {
      handleRegistrationError('Project ID not found');
    }
    try {
      const pushTokenString = (
        await Notifications.getExpoPushTokenAsync({
          projectId,
        })
      ).data;
      console.log('Push token:', pushTokenString);
      return pushTokenString;
    } catch (e: unknown) {
      handleRegistrationError(`${e}`);
    }
  } else {
    handleRegistrationError('Must use physical device for push notifications');
  }
}

function AppContent() {
  const { user, loading, session, storedCredentials } = useAuth();
  const [userAgent, setUserAgent] = useState<string>('');
  const [expoPushToken, setExpoPushToken] = useState('');
  const [canGoBack, setCanGoBack] = useState(false);
  const webViewRef = useRef<WebView>(null);

  useEffect(() => {
    const generateUserAgent = () => {
      const deviceModel = Device.modelName || 'Unknown Device';
      const osVersion = Device.osVersion || '16.0';

      if (Platform.OS === 'ios') {
        return `Mozilla/5.0 (iPhone; CPU iPhone OS ${osVersion.replace('.', '_')} like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/${osVersion} Mobile/15E148 Safari/604.1`;
      } else {
        return `Mozilla/5.0 (Linux; Android ${osVersion}; ${deviceModel}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36`;
      }
    };

    setUserAgent(generateUserAgent());
  }, []);

  // Handle hardware back button (Android) and enable back navigation
  useEffect(() => {
    const backAction = () => {
      if (canGoBack && webViewRef.current) {
        webViewRef.current.goBack();
        return true; // Prevent default behavior
      }
      return false; // Allow default behavior (exit app)
    };

    if (Platform.OS === 'android') {
      const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);
      return () => backHandler.remove();
    }
  }, [canGoBack]);

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="auto" />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
        </View>
      </SafeAreaView>
    );
  }

  if (!user) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="auto" />
        <AuthScreen />
      </SafeAreaView>
    );
  }

  // Create the injected JavaScript with Supabase credentials
  const supabaseHost = 'oakrnplafjvuupqxdokt.supabase.co';
  const authTokenKey = `sb-${supabaseHost}-auth-token`;

  const injectedJavaScript = `
    // Store push token globally
    window.expoPushToken = "${expoPushToken}";

    // Store Supabase credentials in localStorage
    ${session ? `
    const supabaseCredentials = ${JSON.stringify({
      access_token: session.access_token,
      refresh_token: session.refresh_token,
      expires_at: session.expires_at,
      token_type: session.token_type,
      user: session.user
    })};

    localStorage.setItem('${authTokenKey}', JSON.stringify(supabaseCredentials));
    console.log('Supabase credentials injected into WebView localStorage:', supabaseCredentials);
    ` : ''}

    // Function to send push token to React app
    function sendPushTokenToReact() {
      if (window.expoPushToken) {
        localStorage.setItem('expoPushToken', window.expoPushToken);
        window.dispatchEvent(new CustomEvent('expoPushTokenReceived', {
          detail: { token: window.expoPushToken }
        }));
        console.log('Push token sent to React app:', window.expoPushToken);
      }
    }

    // Send token immediately when script loads
    sendPushTokenToReact();

    // Listen for requests from React app
    window.addEventListener('message', function(event) {
      if (event.data.type === 'GET_PUSH_TOKEN') {
        window.postMessage({type: 'PUSH_TOKEN', token: window.expoPushToken}, '*');
      }
    });

    // Enable iOS back gesture and touch interactions
    document.addEventListener('DOMContentLoaded', function() {
      sendPushTokenToReact();
      document.body.style.webkitTouchCallout = 'none';
      document.body.style.webkitUserSelect = 'none';
      document.body.style.userSelect = 'none';
      document.body.style.webkitOverflowScrolling = 'touch';

      if (!document.querySelector('meta[name="viewport"]')) {
        var meta = document.createElement('meta');
        meta.name = 'viewport';
        meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover';
        document.getElementsByTagName('head')[0].appendChild(meta);
      }
    });

    true; // Required for iOS
  `;

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />
      <WebView
        ref={webViewRef}
        source={{ uri: 'https://app.emergent.sh?mobile=true' }}
        style={styles.webview}
        userAgent={userAgent}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        startInLoadingState={true}
        scalesPageToFit={true}
        injectedJavaScript={injectedJavaScript}
        sharedCookiesEnabled={true}
        thirdPartyCookiesEnabled={true}
        mixedContentMode="compatibility"
        cacheEnabled={true}
        allowsBackForwardNavigationGestures={true}
        bounces={true}
        scrollEnabled={true}
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        pullToRefreshEnabled={true}
        onNavigationStateChange={(navState) => {
          setCanGoBack(navState.canGoBack);
        }}
        onMessage={(event) => {
          try {
            const data = JSON.parse(event.nativeEvent.data);
            console.log('Message from WebView:', data);

            switch (data.type) {
              case 'REQUEST_PUSH_TOKEN':
                if (webViewRef.current && expoPushToken) {
                  webViewRef.current.postMessage(JSON.stringify({
                    type: 'PUSH_TOKEN_RESPONSE',
                    token: expoPushToken
                  }));
                }
                break;
              case 'OPEN_EXTERNAL_URL':
                Linking.openURL(data.url);
                break;
              case 'PUSH_TOKEN_RECEIVED':
                console.log('Web app confirmed push token receipt:', data.token);
                break;
              default:
                console.log('Unknown message type:', data.type);
            }
          } catch (error) {
            console.error('Error parsing message from WebView:', error);
          }
        }}
        {...(Platform.OS === 'ios' && {
          allowsInlineMediaPlayback: true,
          mediaPlaybackRequiresUserAction: false,
          automaticallyAdjustContentInsets: false,
          contentInsetAdjustmentBehavior: 'never',
        })}
      />
    </SafeAreaView>
  );
}
export default function RootLayout() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  webview: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
