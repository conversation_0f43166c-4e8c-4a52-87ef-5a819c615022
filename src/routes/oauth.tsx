import { useEffect, useState } from "react";
import SparkleSV<PERSON> from "@/assets/oauth/sparkle.svg";
import EnvelopeSVG from "@/assets/oauth/envelope-default.svg";
import NftSVG from "@/assets/oauth/nft-default.svg";
import LayerSVG from "@/assets/oauth/layer-three.svg";
import BGImage from "@/assets/oauth/image 4.png";
import GoogleSVG from "@/assets/oauth/google.svg";
import ShieldSVG from "@/assets/oauth/foundation_shield.svg";
import Emergent from "@/assets/oauth/emergent.svg";
import { config } from "@/config";

interface CardInfo {
  title: string;
  image: string;
}

const DATA: CardInfo[] = [
  
  
  {
    title: "Securely verify your identity",
    image: SparkleSVG,
  },
  {
    title: "Retrieve your email information",
    image: EnvelopeSVG,
  },
  {
    title: "Access your basic profile details",
    image: NftSVG,
  },
  {
    title: "Maintain your login sessions",
    image: LayerSVG,
  },
];
export default function OAuth() {
  const [redirectUrl, setRedirectUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [status, setStatus] = useState<'initial' | 'processing' | 'success' | 'error'>('initial');
  const [message, setMessage] = useState<string>('');

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const redirectParam = urlParams.get('redirect');

    if (!redirectParam) {
      console.error('OAuth: No redirect URL provided');
      setError('No redirect URL provided. Authentication cannot proceed.');
      return;
    }

    console.log('OAuth: Got redirect URL from params:', redirectParam);
    setRedirectUrl(redirectParam);
  }, []);



  const handleGoogleAuth = () => {
    if (!redirectUrl) {
      console.error('OAuth: No redirect URL available');
      setError('No redirect URL available. Please refresh and try again.');
      return;
    }
    const backendUrl = `${config.apiBaseUrl}/auth/v1/env/oauth?redirect=${encodeURIComponent(redirectUrl)}`;
    window.location.href = backendUrl;
  };

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-white">
        <div className="text-center">
          <h2 className="mb-4 text-xl font-medium text-red-600">Authentication Error</h2>
          <p className="mb-4 text-gray-600">{error}</p>
          <button
            type="button"
            onClick={() => window.close()}
            className="px-4 py-2 text-white bg-black rounded hover:bg-[#333333]"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="flex md:flex-row flex-col w-full min-h-screen max-h-screen Z-[999]">
        
        <div className="relative flex flex-col justify-end flex-[44] md:h-[100vh] md:px-[56px] md:pb-[56px]">
          <img
            src={BGImage}
            alt="Background"
            className="absolute top-0 left-0 object-cover w-full h-full"
          />
          <span className="z-10 p-6 pb-5 text-[18px] font-semibold">This app will be able to :</span>
          <div className="flex flex-row gap-5 px-6 pb-8 overflow-y-auto md:grid md:grid-cols-2">
            {DATA.map((item, index) => (
              <div
                key={index}
                className="flex flex-col max-md:min-h-[140px] max-md:min-w-[200px] rounded-[8px] shadow-white backdrop-blur-[50px] p-5 pt-6 gap-[34px]"
              >
                <img src={item.image} alt={item.title} className="w-6 h-6" />
                <p className="leading-[24px] tracking-[-0.2px] text-[#FFFFFFCC] font-semibold text-[18px]">
                  {item.title}
                </p>
              </div>
            ))}
          </div>
        </div>
        <div className="flex-[66] md:h-[100vh] max-md:justify-between p-6 pb-[40px] flex flex-col md:p-[40px] h-full bg-[#FFFFFF]">
          <div className="flex flex-col items-center justify-center w-full h-full">
            <div className="flex max-md:mt-[40px] gap-[40px] items-center flex-col justify-center max-w-[400px]">
            <span className="font-nothing tracking-[0.04em] uppercase text-black">
              Log In to your app
            </span>

            {status === 'initial' && (
              <button
                type="button"
                title="Google Signin"
                onClick={handleGoogleAuth}
                className="rounded-full p-[18px] gap-4 w-full bg-black flex md:min-w-[400px] hover:bg-[#333333]"
              >
                <img src={GoogleSVG} alt="Google" className="w-6 h-6" />
                <div className="flex justify-center w-full">
                  <span className="font-medium text-[16px] tracking-[-0.25px]">
                    Continue with Google
                  </span>
                </div>
              </button>
            )}

            {status === 'processing' && (
              <div className="flex flex-col items-center gap-4 w-full md:min-w-[400px]">
                <div className="w-8 h-8 border-b-2 border-black rounded-full animate-spin"></div>
                <p className="font-medium text-gray-600">{message}</p>
              </div>
            )}

            {status === 'success' && (
              <div className="flex flex-col items-center gap-4 w-full md:min-w-[400px]">
                <div className="flex items-center justify-center w-8 h-8 bg-green-500 rounded-full">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <p className="font-medium text-green-600">{message}</p>
              </div>
            )}

            {status === 'error' && (
              <div className="flex flex-col items-center gap-4 w-full md:min-w-[400px]">
                <div className="flex items-center justify-center w-8 h-8 bg-red-500 rounded-full">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </div>
                <p className="mb-4 font-medium text-red-600">{message}</p>
                <button
                  type="button"
                  onClick={() => {
                    setStatus('initial');
                    setMessage('');
                  }}
                  className="px-4 py-2 text-white bg-black rounded hover:bg-gray-800"
                >
                  Try Again
                </button>
              </div>
            )}

            <p className="text-[#45474D] opacity-50 text-[11px] font-['Inter'] text-center leading-[20px] mt-6">
              By continuing, you agree to emergent{" "}
              <a href="/terms-of-service" target="_blank" rel="noreferrer noopener" className="font-medium text-black underline">
                Terms of Service
              </a>{" "}
              and{" "}
              <a href="/privacy-policy" target="_blank" rel="noreferrer noopener" className="font-medium text-black underline">
                Privacy Policy
              </a>
              . This site is protected by reCAPTCHA Enterprise and the Google{" "}
              <a href="https://policies.google.com/privacy" target="_blank" rel="noreferrer noopener" className="font-medium text-black underline">
                Privacy Policy
              </a>{" "}
              and{" "}
              <a href="https://policies.google.com/terms"  target="_blank" rel="noreferrer noopener"className="font-medium text-black underline">
                Terms of service
              </a>{" "}
              apply.
            </p>
          </div>
          </div>

          <div className="flex flex-col items-center gap-4">
            <div className="flex items-center gap-[6px]">
              <img src={ShieldSVG} alt="Shield" className="" />
              <span className="text-[#04B200] tracking-[0.04em] text-[12px] font-nothing uppercase">
                Log In secured by
              </span>
            </div>
            <a href="https://emergent.sh" target="_blank" rel="noreferrer noopener">
              <img src={Emergent} alt="Emergent" className="h-6 " />
            </a>
          </div>
        </div>
      </div>
    </>
  );
}


