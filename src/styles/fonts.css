@font-face {
  font-family: '<PERSON><PERSON>';
  src: url('../assets/fonts/brockmann/<PERSON>mann-Regular.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: '<PERSON><PERSON>';
  src: url('../assets/fonts/brockmann/<PERSON>mann-RegularItalic.otf') format('opentype');
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'Brockmann';
  src: url('../assets/fonts/brockmann/Brockmann-Medium.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Brockmann';
  src: url('../assets/fonts/brockmann/Brockmann-MediumItalic.otf') format('opentype');
  font-weight: 500;
  font-style: italic;
}

@font-face {
  font-family: 'Brockmann';
  src: url('../assets/fonts/brockmann/<PERSON>mann-SemiBold.otf') format('opentype');
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'Brockmann';
  src: url('../assets/fonts/brockmann/Brockmann-SemiBoldItalic.otf') format('opentype');
  font-weight: 600;
  font-style: italic;
}

@font-face {
  font-family: 'Brockmann';
  src: url('../assets/fonts/brockmann/Brockmann-Bold.otf') format('opentype');
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Brockmann';
  src: url('../assets/fonts/brockmann/Brockmann-BoldItalic.otf') format('opentype');
  font-weight: bold;
  font-style: italic;
}

@font-face {
  font-family: 'Berkeley Mono Trial';
  src: url('../assets/fonts/BerkeleyMonoTrial-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Nothing';
  src: url('../assets/fonts/nothing-font-5x7.otf') format('opentype'),
       url('../assets/fonts/nothing-font-5x7.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}