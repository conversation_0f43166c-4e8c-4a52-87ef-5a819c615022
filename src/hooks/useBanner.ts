import { useState, useCallback, useRef, useEffect } from 'react';
import { AlertLevel } from '@/components/ui/alert-banner';

export interface BannerConfig {
  show: boolean;
  level: AlertLevel;
  content: string;
  iconUrl?: string;
  dismissible?: boolean;
}

export const useBanner = (initialConfig?: Partial<BannerConfig>) => {
  const [bannerConfig, setBannerConfig] = useState<BannerConfig>({
    show: false,
    level: 'info',
    content: '',
    dismissible: true,
    ...initialConfig,
  });

  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const showBanner = useCallback((config: Omit<BannerConfig, 'show'>) => {
    setBannerConfig({
      ...config,
      show: true,
    });

    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set auto-dismiss timer for 1 minute (60000ms)
    timeoutRef.current = setTimeout(() => {
      setBannerConfig(prev => ({ ...prev, show: false }));
    }, 60000);
  }, []);

  const hideBanner = useCallback(() => {
    setBannerConfig(prev => ({ ...prev, show: false }));

    // Clear the timeout when manually dismissed
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  const updateBanner = useCallback((updates: Partial<BannerConfig>) => {
    setBannerConfig(prev => ({ ...prev, ...updates }));
  }, []);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Convenience methods for different alert levels
  const showInfo = useCallback((content: string, iconUrl?: string, dismissible = true) => {
    showBanner({ level: 'info', content, iconUrl, dismissible });
  }, [showBanner]);

  const showWarning = useCallback((content: string, iconUrl?: string, dismissible = true) => {
    showBanner({ level: 'warning', content, iconUrl, dismissible });
  }, [showBanner]);

  const showError = useCallback((content: string, iconUrl?: string, dismissible = true) => {
    showBanner({ level: 'error', content, iconUrl, dismissible });
  }, [showBanner]);

  const showSuccess = useCallback((content: string, iconUrl?: string, dismissible = true) => {
    showBanner({ level: 'success', content, iconUrl, dismissible });
  }, [showBanner]);

  return {
    bannerConfig,
    showBanner,
    hideBanner,
    updateBanner,
    showInfo,
    showWarning,
    showError,
    showSuccess,
  };
};
