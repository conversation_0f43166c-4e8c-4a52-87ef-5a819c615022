import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { X, Loader2 } from "lucide-react";
import { useDeploy } from "@/hooks/useDeploy";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { agent<PERSON>pi } from "@/services/agentApi";
import { useDeploymentTimer } from "@/hooks/useDeploymentTimer";
import { useDeploymentModals } from "@/hooks/useDeploymentModals";
import { useDeploymentActions } from "@/hooks/useDeploymentActions";
import { DeploymentStepsView } from "./DeploymentStepsView";
import { NoDeploymentView } from "./NoDeploymentView";
import { DeploymentHistoryView } from "./DeploymentHistoryView";
import {
  stepNameMap,
  runningStepNameMap,
} from "@/utils/deploymentConstants";
import DeployArrowDark from "@/assets/arrow_upload_ready_dark.svg";

// @ts-ignore
import animatedSpinner from "../../assets/animated-spinner.gif";
import WebImage from "@/assets/webImage.svg";
import DeployCloud from "@/assets/DeployCloud.svg";

import ModalOverlay from "./ModalOverlay";
import DeploymentBar from "./DeploymentBar";
import DeploymentFooter from "./DeploymentFooter";
import EnvVariablesSection from "./EnvVariablesSection";
import { EnvValues } from "@/store/deploySlice";
import { cn } from "@/lib/utils";
import DeployTime from "@/assets/DeployTime.svg";
import DustBin from "@/assets/DustBin.svg";
import PulseDot from "../PulseDot";

import ReplaceIcon from "@/assets/deployment/replace.svg";
import ReplaceIconGrey from "@/assets/deployment/replace_grey.svg";
import DeployedCard, { type IDeployment } from "../DeployedCard";
import ReplaceIconBlack from "@/assets/deployment/replace_black.svg";

import PreviewOrange from "@/assets/deployment/carbon_view-filled.svg";
import DustBinRed from "@/assets/deployment/DustbinRed.svg";

import ArrowLeft from "@/assets/deployment/GoBack.svg";
import BuildLogsModal from "../deploy/buildLogsModal";
import { formatTimeAgo } from "@/utils/deploymentUtils";

interface DeployPanelProps {
  isOpen: boolean;
  onClose: () => void;
  jobId?: string;
  agentStatus?: string;
  handleJobClick?: (job: any) => void;
  onShareLogs?: (logs: string[]) => void;
}

export function DeployPanel({
  isOpen,
  onClose,
  jobId,
  agentStatus,
  handleJobClick,
  onShareLogs,
}: DeployPanelProps) {
 
  const { toast } = useToast();
  const {
    deployStatus,
    latestRunStatus,
    handleDeploy,
    loading,
    deploymentSteps,
    deploymentHistory,
    currentStepIndex,
    deployUrl,
    customDomainUrl,
    runId,
    checkDeployStatus,
    loadDeploymentHistory,
    loadEnvironmentVariables,
    saveEnvironmentVariables,
    updateEnvironmentVariables,
    envs,
    shutDownDeployment,
    loadDeployLogs,
    deployLogs,
  } = useDeploy(jobId);

  // State to track if env variables are synced with production
  const [envSyncedWithProduction, setEnvSyncedWithProduction] = useState(true);
  // Local loading state for UI operations
  const [localLoading, setLocalLoading] = useState(false);
  // Use deployment timer hook
  const { elapsedTime } = useDeploymentTimer({
    jobId,
    deployStatus,
    latestRunStatus,
  });

   // Enhanced onClose function to close all modals
  const handleClose = () => {
    // Close all modals first
    setModalStatus({
      deployModal: false,
      shutDownModal: false,
      instantRollbackModal: false,
      unlinkDomainModal: false,
      redeployModal: false,
      replaceDeploymentModal: false,
      confirmReplaceDeploymentModal: false,
      buildLogsModal: false,
    });

    loadEnvironmentVariables();
    // Then close the panel
    onClose();
  };

  // Use deployment modals hook
  const {
    modalStatus,
    setModalStatus,
    domainToUnlink,
    replaceDeployments,
    replaceHoveredCard,
    setReplaceHoveredCard,
    selectedReplacement,
    setSelectedReplacement,
    loadingReplaceDeployments,
    isDeploymentTransitioning,
    setIsDeploymentTransitioning,
    rollbackTargetId,
    setRollbackTargetId,
    handleDeployClick,
    handleShutDown,
    handleReDeploy,
    handleOpenUnlinkDomainModal,
    confirmUnlinkDomain,
    fetchReplaceDeployments,
  } = useDeploymentModals({
    jobId,
    agentStatus,
    checkDeployStatus: checkDeployStatus as any,
    isOpen, // Pass isOpen to track when panel opens
    deployStatus, // Pass deployStatus to watch for changes
  });

  // Use deployment actions hook
  const { handleReplacement, executeRedeployment } = useDeploymentActions({
    jobId,
    agentStatus,
    handleDeploy,
    checkDeployStatus: checkDeployStatus as any,
    updateEnvironmentVariables,
    selectedReplacement,
    setModalStatus,
    modalStatus,
    setIsDeploymentTransitioning,
    setLocalLoading,
  });

  const handleLogsClick = async ()=>{
    console.log("xoxo Logs Clicked");

    // Call the logs API when opening the modal
    if (jobId && loadDeployLogs) {
      try {
        await loadDeployLogs(jobId);
      } catch (error) {
        console.error("Error loading logs:", error);
      }
    }

    setModalStatus({
      ...modalStatus,
      buildLogsModal: true,
    });
  }

  const [envValues, setEnvValues] = useState<EnvValues[]>(envs);

  useEffect(() => {
    if (isOpen) {
      fetchReplaceDeployments();
    }
  }, [isOpen]);

  // Update local state when Redux state changes
  useEffect(() => {
    if (envs && envs.length > 0) {
      setEnvValues(envs);
    }
  }, [envs]);

  // Force a status check when the panel is opened
  useEffect(() => {
    if (isOpen && jobId) {
      // First check deployment status - use false for silent to ensure state updates
      checkDeployStatus(jobId, false);

      // Then load environment variables and deployment history
      loadEnvironmentVariables();
      if (deployStatus !== "not_deployed") {
        loadDeploymentHistory();
      }
    }
  }, [
    isOpen,
    jobId,
    checkDeployStatus,
    loadEnvironmentVariables,
    loadDeploymentHistory,
  ]);

  useEffect(()=>{
    loadDeploymentHistory();
    // Then load environment variables and deployment history
    loadEnvironmentVariables();
  },[deployStatus, latestRunStatus])

  useEffect(() => {
    if (!jobId) return;

    if (deployStatus === "running" || latestRunStatus === "running") {
      checkDeployStatus(jobId, false);
    } else if (deployStatus === "success" || deployStatus === "failed") {
      checkDeployStatus(jobId, false)
        .then(() => {
          if (deployStatus === "success") {
            loadDeploymentHistory();
          }
        })
        .catch((error) => {
          console.error(`[DeployPanel] Error checking deploy status:`, error);
        });
    }
  }, [
    deployStatus,
    latestRunStatus,
    jobId,
    checkDeployStatus,
    loadDeploymentHistory,
  ]);

  useEffect(()=>{
    fetchReplaceDeployments();
  },[deployStatus, latestRunStatus])

  const latestSuccessfulDeployment = deploymentHistory.find(
    (run) => run.status === "success"
  );

  // Function to get job details and open task (similar to DeployedApps.tsx)
  const getJobDetails = async (deployment: IDeployment) => {
    if (!handleJobClick) return;

    try {
      const response = await agentApi.getJob(deployment.id);
      const job = response.data;
      handleJobClick(job);
    } catch (error) {
      console.error("Error fetching job details:", error);
    }
  };

  return (
    <div
      className={cn("w-full h-[calc(100%-3.5rem)] bg-[#0F0F10] border-l border border-[#242424]/60 max-md:inset-0 max-md:absolute z-[49]", isOpen ? "block":"hidden")}
    >
          <div
            className={cn(
              "relative flex flex-col justify-between min-h-full max-h-[100%] overflow-y-scroll overflow-clip",
              deployStatus !== "running" &&
                latestRunStatus !== "running" &&
                deployStatus != "not_deployed" &&
                !loading
                ? "pb-[5rem]"
                : ""
            )}
          >
            {/* // Header */}
            <div className="px-3 md:px-6 md:pt-5 z-[49] sticky top-0 bg-[#111112] py-3  md:py-5 max-h-[69px] flex items-center justify-between border-b border-[#1F1F1F]">
              <div className="flex items-center gap-2">
                <img
                  src={DeployCloud}
                  alt="Deploy"
                  className="w-5 h-5 md:w-6 md:h-6"
                />
                <div className="text-white font-medium text-[14px]  md:text-[18px]">
                  {deployStatus === "running" || latestRunStatus === "running"
                    ? "Deployment in progress..."
                    : "Deployments"}
                </div>
              </div>
              <button
                title="Close"
                type="button"
                onClick={handleClose}
                className="text-gray-400 hover:text-white"
              >
                <X className="w-5 h-5 md:w-6 md:h-6" />
              </button>
            </div>

            <div className="flex flex-col flex-1 h-full overflow-y-auto">
              {/* No deployments state */}
              {deployStatus === "not_deployed" && !loading && (
                <NoDeploymentView
                  onDeployClick={handleDeployClick}
                  loading={loading}
                  localLoading={localLoading}
                />
              )}
              {/* Deployment in progress state */}
              {(deployStatus === "running" || latestRunStatus === "running") &&
                !isDeploymentTransitioning && (
                  <DeploymentStepsView
                    deploymentSteps={deploymentSteps}
                    currentStepIndex={currentStepIndex}
                    deployStatus={deployStatus}
                    latestRunStatus={latestRunStatus}
                    stepNameMap={stepNameMap}
                    runningStepNameMap={runningStepNameMap}
                  />
                )}

              {/* Loading state during deployment transition */}
              {isDeploymentTransitioning && (
                <motion.div
                  className="flex flex-col items-center justify-center h-full py-20"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  key="deployment-loading"
                >
                  <div className="flex flex-col items-center gap-4">
                    <img
                      src={animatedSpinner}
                      alt="Loading"
                      className="w-10 h-10"
                    />
                    <p className="text-[#DDDDE6] text-lg">
                      Preparing deployment...
                    </p>
                  </div>
                </motion.div>
              )}

              {/* Deployment history state */}
              {(deployStatus === "failed" || deployStatus === "success") &&
                !(latestRunStatus === "running") &&
                !isDeploymentTransitioning && (
                  <DeploymentHistoryView
                    deploymentHistory={deploymentHistory}
                    jobId={jobId || ""}
                    handleDeploy={handleDeploy}
                    deployStatus={deployStatus}
                    runId={runId}
                    deployUrl={deployUrl}
                    customDomainUrl={customDomainUrl}
                    latestRunStatus={latestRunStatus}
                    agentStatus={agentStatus}
                    onRollbackClick={(id) => {
                      setModalStatus({
                        ...modalStatus,
                        instantRollbackModal: true,
                      });
                      setRollbackTargetId(id);
                    }}
                    onUnlinkDomain={handleOpenUnlinkDomainModal}
                    formatTimeAgo={formatTimeAgo}
                    handleLogsClick={handleLogsClick}
                    deployLogs={deployLogs}
                    loadDeployLogs={loadDeployLogs}
                  />
                )}

              {/* Instant Rollback Modal */}
              <ModalOverlay
                isOpen={modalStatus.instantRollbackModal}
                onClose={() =>
                  setModalStatus({
                    ...modalStatus,
                    instantRollbackModal: false,
                  })
                }
                position="bottom"
                blurStrength="sm"
                contained={true}
                className="m-3 md:m-6 rounded-[16px] overflow-clip bg-[#18181A]"
              >
                <div className="p-3 md:p-6 md:py-[20px] border-[#242424] bg-[#18181A] border-b-[1px]">
                  <div className="text-[14px] md:text-[18px] font-medium text-[#E6E6E6]">
                    Rollback to Previous Deployment
                  </div>
                  <button
                    type="button"
                    title="Close"
                    onClick={() =>
                      setModalStatus({
                        ...modalStatus,
                        instantRollbackModal: false,
                      })
                    }
                    className="absolute text-gray-400 top-3 right-3 md:top-4 md:right-4 hover:text-white"
                  >
                    <X className="w-5 h-5 md:w-6 md:h-6" />
                  </button>
                </div>
                <div className="p-3 md:p-6">
                  <p className="font-medium font-['Inter'] text-[13px] md:text-[16px] text-[#939399]">
                    Replace your current production with a previously successful
                    deployment. Your site will remain online with minimal
                    downtime.
                  </p>

                  {/* Current active deployment */}
                  {deploymentHistory.length > 0 && (
                    <div className="mt-6 mb-6">
                      <div>
                        <DeploymentBar
                          jobId={jobId || ""}
                          deployStatus={"success"}
                          currentActive={true}
                          deployTime={formatTimeAgo(
                            deploymentHistory.find((run) => run.id === runId)
                              ?.updated_at || ""
                          )}
                          nextExist={false}
                          deploymentLink={deployUrl}
                          deploymentRun={deploymentHistory.find(
                            (run) => run.id === runId
                          )}
                          latestRunStatus={latestRunStatus}
                          inRollbackModal={true}
                          isRollbackTarget={false}
                          handleLogsClick= {handleLogsClick}
                        />
                      </div>
                    </div>
                  )}

                  {/* Deployment to rollback to */}
                  {deploymentHistory.length > 1 && (
                    <div className="mt-6">
                      <div>
                        <DeploymentBar
                          jobId={jobId || ""}
                          deployStatus={"success"}
                          currentActive={false}
                          deployTime={formatTimeAgo(
                            deploymentHistory.find(
                              (run) => run.id === rollbackTargetId
                            )?.updated_at || ""
                          )}
                          nextExist={false}
                          deploymentLink={deployUrl}
                          deploymentRun={deploymentHistory.find(
                            (run) => run.id === rollbackTargetId
                          )}
                          latestRunStatus={latestRunStatus}
                          inRollbackModal={true}
                          isRollbackTarget={true}
                          handleLogsClick= {handleLogsClick}
                        />
                      </div>
                    </div>
                  )}
                </div>

                {/* Footer Bar */}
                <div className="p-2 md:p-4 md:min-h-[96px] bg-[#18181A] border-t border-[#242424] flex flex-col">
                  <div className="flex items-center justify-end w-full md:justify-between">
                    <div className="hidden md:flex items-center gap-2 text-[#737780] text-sm">
                      <span>Rollback usually takes around 1-3 mins.</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        onClick={() =>
                          setModalStatus({
                            ...modalStatus,
                            instantRollbackModal: false,
                          })
                        }
                        className="bg-[#272729] text-white hover:bg-[#272729]/90 rounded-full px-3 py-2 text-[14px] md:text-[16px]   md:px-4 md:py-2 text-sm flex items-center gap-1"
                      >
                        <span>Cancel</span>
                      </Button>
                      <Button
                        onClick={async () => {
                          if (
                            jobId &&
                            deploymentHistory.length > 1 &&
                            deploymentHistory.find(
                              (run) => run.id === rollbackTargetId
                            )?.params?.image
                          ) {
                            toast({
                              title: "Rolling back...",
                              description:
                                "Please wait while we rollback your deployment.",
                              duration: 2000,
                            });
                            try {
                              // First check the latest deployment status
                              try {
                                const deployStatusResponse =
                                  await agentApi.getDeployStatus(jobId);

                                // Check if there's an ongoing deployment in latest_run
                                if (
                                  deployStatusResponse.latest_run &&
                                  deployStatusResponse.latest_run.status ===
                                    "running"
                                ) {
                                  toast({
                                    title: "Deployment in Progress",
                                    description:
                                      "You can only have one deployment in progress at a time. Please wait for the current deployment to complete before rolling back.",
                                    variant: "destructive",
                                    duration: 1000,
                                  });
                                  return;
                                }

                                // If we get here, it's safe to proceed with rollback
                                // Call the API to deploy with the image from the selected deployment
                                await agentApi.deployApp(
                                  jobId,
                                  deploymentHistory.find(
                                    (run) => run.id === rollbackTargetId
                                  )?.params?.image
                                );

                                // Close the modal after initiating rollback
                                setModalStatus({
                                  ...modalStatus,
                                  instantRollbackModal: false,
                                });

                                // Update status to running immediately
                                if (jobId) {
                                  checkDeployStatus(jobId, false);
                                }

                                // Show success message
                                toast({
                                  title: "Rollback Initiated",
                                  description:
                                    "Your rollback has been initiated and will be completed shortly.",
                                  duration: 2000,
                                });
                              } catch (error) {
                                console.error(
                                  "Error checking deployment status:",
                                  error
                                );
                                // If we can't check the status, try to rollback anyway
                                await agentApi.deployApp(
                                  jobId,
                                  deploymentHistory.find(
                                    (run) => run.id === rollbackTargetId
                                  )?.params?.image
                                );

                                // Close the modal after initiating rollback
                                setModalStatus({
                                  ...modalStatus,
                                  instantRollbackModal: false,
                                });

                                // Update status to running immediately
                                if (jobId) {
                                  checkDeployStatus(jobId, false);
                                }

                                // Show success message
                                toast({
                                  title: "Rollback Initiated",
                                  description:
                                    "Your rollback has been initiated and will be completed shortly.",
                                  duration: 2000,
                                });
                              }
                            } catch (error: any) {
                              console.error("Rollback error details:", error);

                              // Extract error message from various possible structures
                              let errorMessage =
                                "Failed to rollback deployment";

                              if (error?.message) {
                                errorMessage = error.message;

                                // Check if the error message contains the deploy in progress text
                                if (
                                  errorMessage.includes("deploy in progress")
                                ) {
                                  toast({
                                    title: "Deployment in Progress",
                                    description:
                                      "You can only have one deployment in progress at a time. Please wait for the current deployment to complete before rolling back.",
                                    variant: "destructive",
                                    duration: 2000,
                                  });
                                  return;
                                }
                              }

                              // Try to extract from response data if available
                              if (error?.response?.data) {
                                const data = error.response.data;
                                if (data.detail) errorMessage = data.detail;

                                // Check for deployment in progress error
                                if (
                                  errorMessage.includes("deploy in progress")
                                ) {
                                  toast({
                                    title: "Deployment in Progress",
                                    description:
                                      "You can only have one deployment in progress at a time. Please wait for the current deployment to complete before rolling back.",
                                    variant: "destructive",
                                    duration: 2000,
                                  });
                                  return;
                                }
                              }

                              // Show error message
                              toast({
                                title: "Rollback Failed",
                                description: errorMessage,
                                variant: "destructive",
                                duration: 2000,
                              });
                            }
                          }
                        }}
                        className="flex items-center gap-3 px-3 py-2 text-sm text-black bg-white rounded-full md:px-4 hover:bg-white/90"
                      >
                        Confirm Rollback
                      </Button>
                    </div>
                  </div>
                </div>
              </ModalOverlay>

              {/* Shutdown Confirmation Modal */}
              <ModalOverlay
                isOpen={modalStatus.shutDownModal}
                onClose={() =>
                  setModalStatus({
                    ...modalStatus,
                    shutDownModal: false,
                  })
                }
                position="bottom"
                blurStrength="sm"
                contained={true}
                className="m-3 md:m-6 rounded-[16px] overflow-clip bg-[#18181A]"
              >
                <div className="p-3 md:p-4 md:py-[24px] border-[#242424] bg-[#18181A] border-b-[1px]">
                  <div className="text-[14px] md:text-[18px] font-medium text-[#E6E6E6]">
                    Confirm Shutdown
                  </div>
                  <button
                    type="button"
                    title="Close"
                    onClick={() =>
                      setModalStatus({
                        ...modalStatus,
                        shutDownModal: false,
                      })
                    }
                    className="absolute text-gray-400 top-3 right-3 md:top-4 md:right-4 hover:text-white"
                  >
                    <X className="w-5 h-5 md:w-7 md:h-7" />
                  </button>
                </div>
                <div className="p-3 md:p-6">
                  <p className="font-medium font-['Inter'] text-[14px] md:text-[16px] text-[#939399]">
                    Are you sure you want to shut down this deployment? This
                    will make your application immediately unavailable to users
                    and stop all recurring charges.
                  </p>
                </div>

                {/* Footer Bar */}
                <div className="p-4 min-h-[96px] bg-[#18181A] border-t border-[#242424] flex flex-col">
                  <div className="flex items-center justify-between w-full">
                    <div className="flex  text-[12px] items-center gap-2 text-[#737780] md:text-sm">
                      <span>This action cannot be undone.</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        onClick={() =>
                          setModalStatus({
                            ...modalStatus,
                            shutDownModal: false,
                          })
                        }
                        className="bg-[#272729] text-white hover:bg-[#272729]/90 rounded-full px-4 py-2 text-sm flex items-center gap-1"
                      >
                        <span>Cancel</span>
                      </Button>
                      <Button
                        onClick={async () => {
                          if (jobId) {
                            try {
                              // Call the API to shut down the deployment
                              await shutDownDeployment();

                              // Close the modal after initiating shutdown
                              setModalStatus({
                                ...modalStatus,
                                shutDownModal: false,
                              });

                              // Show success message
                              toast({
                                title: "Deployment Shut Down",
                                description:
                                  "Your deployment has been successfully shut down.",
                                duration: 2000,
                              });

                              // Update status to not_deployed
                              if (jobId) {
                                checkDeployStatus(jobId, false);
                              }
                            } catch (error: any) {
                              console.error("Shutdown error details:", error);

                              // Extract error message from various possible structures
                              let errorMessage =
                                "Failed to shut down deployment";

                              if (error?.message) {
                                errorMessage = error.message;
                              }

                              // Try to extract from response data if available
                              if (error?.response?.data) {
                                const data = error.response.data;
                                if (data.detail) errorMessage = data.detail;
                              }

                              // Show error message
                              toast({
                                title: "Shutdown Failed",
                                description: errorMessage,
                                variant: "destructive",
                                duration: 2000,
                              });
                            }
                          }
                        }}
                        className="flex items-center gap-3  md:px-4 md:py-2 text-sm text-white rounded-full bg-[#CC3D3D] hover:bg-red-600"
                      >
                        Confirm Shutdown
                      </Button>
                    </div>
                  </div>
                </div>
              </ModalOverlay>

              {/* Unlink Domain Confirmation Modal */}
              <ModalOverlay
                isOpen={modalStatus.unlinkDomainModal}
                onClose={() =>
                  setModalStatus({
                    ...modalStatus,
                    unlinkDomainModal: false,
                  })
                }
                position="bottom"
                blurStrength="sm"
                contained={true}
                className="m-6 rounded-[16px] overflow-clip bg-[#18181A]"
              >
                <div className="p-8 py-[28px] border-[#242424] bg-[#18181A] border-b-[1px]">
                  <div className="text-[18px] font-medium text-[#E6E6E6]">
                    Confirm Domain Unlinking
                  </div>
                  <button
                    type="button"
                    title="Close"
                    onClick={() =>
                      setModalStatus({
                        ...modalStatus,
                        unlinkDomainModal: false,
                      })
                    }
                    className="absolute text-gray-400 top-8 right-8 hover:text-white"
                  >
                    <X size={24} />
                  </button>
                </div>
                <div className="p-6">
                  <p className="font-medium font-['Inter'] text-[#939399]">
                    Would you like to remove the domain {domainToUnlink} from
                    this App? After continuing, your App will no longer be
                    accessible through this domain.
                  </p>

                  {/* Current domain information */}
                  {domainToUnlink && (
                    <div className="mt-6">
                      <div className="text-[#A199FF] text-md py-4 font-medium mb-2">
                        Connected Domain
                      </div>
                      <motion.div
                        initial={{ opacity: 0, y: 20, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        transition={{
                          type: "spring",
                          stiffness: 400,
                          damping: 30,
                          delay: 0.2,
                        }}
                        className="bg-[#0076FF05] rounded-[12px] border-[#66acff20] overflow-clip border p-4"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <span className="text-[#CCE4FF] font-medium">
                              {domainToUnlink}
                            </span>
                          </div>
                        </div>
                      </motion.div>
                    </div>
                  )}
                </div>

                {/* Footer Bar */}
                <div className="p-4 min-h-[96px] bg-[#18181A] border-t border-[#242424] flex flex-col">
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-2 text-[#737780] text-sm">
                      <span>This action cannot be undone.</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        onClick={() =>
                          setModalStatus({
                            ...modalStatus,
                            unlinkDomainModal: false,
                          })
                        }
                        className="bg-[#272729] text-white hover:bg-[#272729]/90 rounded-full px-4 py-2 text-sm flex items-center gap-1"
                      >
                        <span>Cancel</span>
                      </Button>
                      <Button
                        onClick={confirmUnlinkDomain}
                        className="flex items-center gap-3 px-4 py-2 text-sm text-black bg-red-500 rounded-full hover:bg-red-600"
                      >
                        Confirm Unlinking
                      </Button>
                    </div>
                  </div>
                </div>
              </ModalOverlay>

              {/* Redeployment Confirmation Modal */}
              <ModalOverlay
                isOpen={modalStatus.redeployModal}
                onClose={() =>
                  setModalStatus({
                    ...modalStatus,
                    redeployModal: false,
                  })
                }
                position="bottom"
                blurStrength="sm"
                contained={true}
                className="m-3 md:m-6 rounded-[16px] overflow-clip bg-[#18181A]"
              >
                <div className="p-3 md:p-6 md:py-[20px] border-[#242424] bg-[#18181A] border-b-[1px]">
                  <div className="text-[14px] md:text-[18px] font-medium text-[#E6E6E6]">
                    Confirm Redeployment
                  </div>
                  <button
                    type="button"
                    title="Close"
                    onClick={() =>
                      setModalStatus({
                        ...modalStatus,
                        redeployModal: false,
                      })
                    }
                    className="absolute text-gray-400 top-3 right-3 md:top-4 md:right-4 hover:text-white"
                  >
                    <X className="w-5 h-5 md:w-6 md:h-6" />
                  </button>
                </div>
                <div className="p-3 md:p-6">
                  <p className="font-medium  text-[13px] md:text-[16px] font-['Inter'] text-[#939399]">
                    Are you sure you want to re-deploy your current application?
                    This will replace your current deployment with latest code
                    changes.
                  </p>

                  {/* Current active deployment */}
                  {deploymentHistory.length > 0 && (
                    <div className="mt-2 md:mt-6">
                      <div className="text-[#A199FF] text-md py-2 md:py-4 font-medium mb-2">
                        Current Deployment
                      </div>
                      <motion.div
                        initial={{ opacity: 0, y: 20, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        transition={{
                          type: "spring",
                          stiffness: 400,
                          damping: 30,
                          delay: 0.2,
                        }}
                      >
                        <DeploymentBar
                          jobId={jobId || ""}
                          deployStatus={"success"}
                          currentActive={true}
                          deployTime={formatTimeAgo(
                            deploymentHistory[0].created_at
                          )}
                          nextExist={false}
                          deploymentLink={deployUrl}
                          deploymentRun={deploymentHistory[0]}
                          latestRunStatus={latestRunStatus}
                          inRollbackModal={true}
                          isRollbackTarget={false}
                        />
                      </motion.div>
                    </div>
                  )}
                </div>

                {/* Footer Bar */}
                <div className="p-2 md:p-4 md:min-h-[96px] bg-[#18181A] border-t border-[#242424] flex flex-col">
                  <div className="flex items-center justify-end w-full md:justify-between">
                    <div className="hidden md:flex items-center gap-2 text-[#737780] text-sm max-w-[250px]">
                      <img
                        src={DeployTime}
                        alt="Deploy Time"
                        className="w-8 h-8"
                      />
                      <span>Re-deployment usually takes around 1-3 mins.</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        onClick={() =>
                          setModalStatus({
                            ...modalStatus,
                            redeployModal: false,
                          })
                        }
                        className="bg-[#272729] text-white hover:bg-[#272729]/90 rounded-full px-4 py-2 text-sm flex items-center gap-1"
                      >
                        <span>Cancel</span>
                      </Button>
                      <Button
                        onClick={() =>
                        {
                          executeRedeployment(
                            envSyncedWithProduction,
                            envValues,
                            setEnvSyncedWithProduction
                          );
                          setEnvSyncedWithProduction(false);
                        }
                        }
                        className="flex items-center gap-3 py-2 pl-6 text-sm text-black bg-white rounded-full hover:bg-white/90"
                      >
                        <div className="flex items-center gap-2">
                          <span className="font-semibold">Yes, Re-deploy</span>
                          <img
                            src={DeployArrowDark}
                            alt="Deploy"
                            className="w-5 h-5"
                          />
                        </div>
                      </Button>
                    </div>
                  </div>
                </div>
              </ModalOverlay>

              {/* Deploy Modal */}
              <ModalOverlay
                isOpen={modalStatus.deployModal}
                onClose={() =>
                  setModalStatus({
                    ...modalStatus,
                    deployModal: false,
                  })
                }
                position="bottom"
                blurStrength="sm"
                contained={true}
                className="m-3 md:m-6 rounded-[16px] overflow-clip bg-[#18181A]"
              >
                <div className="p-3 md:p-6 md:py-[20px] border-[#242424] bg-[#18181A] border-b-[1px]">
                  <div className="text-[14px] md:text-[18px] font-medium text-[#E6E6E6]">
                    Confirm Deployment
                  </div>
                  <button
                    type="button"
                    title="Close"
                    onClick={() =>
                      setModalStatus({
                        ...modalStatus,
                        deployModal: false,
                      })
                    }
                    className="absolute text-gray-400 top-3 right-3 md:top-4 md:right-4 hover:text-white"
                  >
                    <X className="w-4 h-4 md:w-6 md:h-6" />
                  </button>
                </div>
                <div className="flex flex-col gap-3 p-3 md:gap-6 md:p-6">
                  <p className="font-medium text-[13px] md:text-[16px] font-['Inter'] text-[#939399]">
                    Your account will be charged{" "}
                    <span className="text-[#f3ca5f] font-semibold font-['Inter']">
                      50 credits/month
                    </span>
                    . You can shut down your deployment anytime to stop charges
                    for the next months.
                  </p>

                  <div className="p-2 md:p-4 bg-[#E68A5C0D] border-[#E68A5C33] border rounded-[10px] flex flex-col gap-2">
                    <div className="flex items-center justify-start gap-1">
                      <img
                        src={PreviewOrange}
                        alt="Replace"
                        className="inline-block w-6 h-6 mr-2"
                      />
                      <span className="text-[#E58A5C] font-['Inter'] font-medium text-[14px] md:text-[16px]">
                        Preview First Recommended
                      </span>
                    </div>
                    <span className="text-[#CCB1A3] font-['Inter'] text-[12px] md:text-base">
                      To avoid deployment issues and wasted credits, please
                      preview your app first to ensure it's working correctly.
                    </span>
                  </div>
                </div>
                <div className="p-4 min-h-[96px] bg-[#18181A] border-t border-[#242424] flex flex-col">
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center gap-2 text-[#737780] max-md:hidden text-[12px] md:text-sm max-w-[250px]">
                      <img
                        src={DeployTime}
                        alt="Deploy Time"
                        className="w-8 h-8"
                      />
                      <span>Deployment usually takes around 5-7 mins.</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        onClick={() =>
                          setModalStatus({
                            ...modalStatus,
                            deployModal: false,
                          })
                        }
                        className="bg-[#272729] text-white hover:bg-[#272729]/90 rounded-full px-3 py-2 md:px-4 md:py-2 text-sm flex items-center gap-1"
                      >
                        <span>Cancel</span>
                      </Button>
                      <Button
                        // @ts-ignore
                        disabled={loading || localLoading}
                        onClick={async () => {
                          try {
                            setModalStatus({
                              ...modalStatus,
                              deployModal: false,
                            });

                            if (jobId) {
                              try {

                               await handleDeploy();
                              } catch (error) {
                                console.error("Error in handleDeploy:", error);
                              }
                            } else {
                              // Initiate deployment

                              await handleDeploy();
                            }
                          } catch (error: any) {
                            toast({
                              title: "Deployment Failed",
                              description:
                                "Failed to deploy. Please try again.",
                              variant: "destructive",
                              duration: 1000,
                            });
                          }
                        }}
                        className="flex items-center gap-1 py-2 pl-6 text-sm text-black bg-white rounded-full md:gap-3 hover:bg-white/90"
                      >
                        <div className="flex items-center gap-2">
                          <span className="font-semibold">Deploy Now</span>{" "}
                          <span className="bg-black text-[#F3CA5F] px-3 py-2 rounded-full">
                            50 Credits
                          </span>
                        </div>
                      </Button>
                    </div>
                  </div>
                </div>
              </ModalOverlay>

              {/* Replace Deployment Modal */}

              <ModalOverlay
                isOpen={modalStatus.replaceDeploymentModal}
                onClose={() =>
                  setModalStatus({
                    ...modalStatus,
                    replaceDeploymentModal: false,
                  })
                }
                position="bottom"
                blurStrength="sm"
                contained={true}
                className="m-3 md:m-6 rounded-[16px] overflow-clip bg-[#18181A]"
              >
                <div className="p-3 md:p-6 md:px-4 md:py-[24px] border-[#242424] bg-[#18181A] border-b-[1px]">
                  <div className="text-[18px] font-medium text-[#E6E6E6]">
                    <img
                      src={ReplaceIconGrey}
                      alt="Replace"
                      className="inline-block w-6 h-6 mr-2 text-[14px] md:text-[16px]"
                    />
                    Replace Deployment
                  </div>
                  <button
                    type="button"
                    title="Close"
                    onClick={() =>
                      setModalStatus({
                        ...modalStatus,
                        replaceDeploymentModal: false,
                      })
                    }
                    className="absolute text-gray-400 top-3 right-3 md:top-4 md:right-4 hover:text-white"
                  >
                    <X className="w-5 h-5 md:w-6 md:h-6" />
                  </button>
                </div>
                <div className="p-3 md:p-6 md:pt-4 max-h-[350px] md:max-h-[500px] overflow-y-scroll flex flex-col gap-6">
                  <p className="text-[13px] md:text-[16px] font-medium font-['Inter'] text-[#939399]">
                    Choose which deployment to replace with your new
                    application. The selected app will be permanently deleted,
                    but you won't be charged any additional fees.
                  </p>
                  {loadingReplaceDeployments ? (
                    <div className="flex items-center justify-center py-8">
                      <img
                        src={animatedSpinner}
                        alt="Loading..."
                        className="w-8 h-8"
                      />
                      <span className="ml-2 text-[#DDDDE6]">
                        Loading deployments...
                      </span>
                    </div>
                  ) : replaceDeployments.length > 0 ? (
                    <div className="grid grid-cols-1 gap-6">
                      {replaceDeployments.map((deployment, index) => (
                        <DeployedCard
                          key={deployment.id}
                          deployment={deployment}
                          index={index}
                          hoveredCard={replaceHoveredCard}
                          onHoverStart={setReplaceHoveredCard}
                          onHoverEnd={() => setReplaceHoveredCard(null)}
                          onClick={getJobDetails}
                          fromRedeployModal={true}
                          onReplaceClick={(clickedDeployment: IDeployment) => {
                            setSelectedReplacement(clickedDeployment);
                            setModalStatus({
                              ...modalStatus,
                              replaceDeploymentModal: false,
                              confirmReplaceDeploymentModal: true,
                            });
                          }}
                        />
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <img
                        src={WebImage}
                        alt="No deployments"
                        className="w-12 h-12 mb-4 opacity-50"
                      />
                      <p className="text-[#737780] font-medium">
                        No deployments available to replace
                      </p>
                      <p className="text-[#737780] text-sm mt-1">
                        You need at least one successful deployment to use this
                        feature
                      </p>
                    </div>
                  )}
                </div>
              </ModalOverlay>

              {/* Confirm Replacement Modal */}

              <ModalOverlay
                isOpen={modalStatus.confirmReplaceDeploymentModal}
                onClose={() =>
                  setModalStatus({
                    ...modalStatus,
                    confirmReplaceDeploymentModal: false,
                  })
                }
                position="bottom"
                blurStrength="sm"
                contained={true}
                className="m-3 md:m-6 rounded-[16px] overflow-clip bg-[#18181A]"
              >
                <div className="p-3 md:p-6 md:px-4 md:py-[24px] border-[#242424] bg-[#18181A] border-b-[1px]">
                  <div className=" text-[14px] md:text-[18px] font-medium text-[#E6E6E6]">
                    <img
                      src={ReplaceIconGrey}
                      alt="Replace"
                      className="inline-block w-6 h-6 mr-2"
                    />
                    Confirm Replacement
                  </div>
                  <button
                    type="button"
                    title="Close"
                    onClick={() =>
                      setModalStatus({
                        ...modalStatus,
                        confirmReplaceDeploymentModal: false,
                      })
                    }
                    className="absolute text-gray-400 top-3 right-3 md:top-4 md:right-4 hover:text-white"
                  >
                    <X className="w-5 h-5 md:w-6 md:h-6" />
                  </button>
                </div>
                <div className="p-3 md:p-6">
                  <p className="font-medium text-[13px] md:text-[16px] font-['Inter'] text-[#939399]">
                    You’re about to replace{" "}
                    <span className="font-medium capitalize text-white/80">
                      {selectedReplacement?.app_name.replaceAll("-", " ")}
                    </span>{" "}
                    with your new application. This action cannot be undone.
                  </p>
                </div>
                <div className="p-3 md:p-4">
                  <div className=" bg-[#FF66661A] border pt-3 p-[6px] border-[#FF666666] rounded-[16px]">
                    <div className="flex items-center gap-2 px-2">
                      <img src={DustBinRed} alt="Replace" className="w-6 h-6" />
                      <span className="text-[#FF6666] font-medium text-[13px] md:text-[16px]">
                        This app will be permanently deleted
                      </span>
                    </div>

                    <div className="bg-[#18181A] overflow-clip rounded-[16px] mt-2">
                      {selectedReplacement && (
                        <DeployedCard
                          deployment={selectedReplacement}
                          index={0}
                          hoveredCard={replaceHoveredCard}
                          onHoverStart={setReplaceHoveredCard}
                          onHoverEnd={() => setReplaceHoveredCard(null)}
                          onClick={getJobDetails}
                          allowHover={false}
                        />
                      )}
                    </div>
                  </div>
                </div>
                <div className="md:p-4 p-3 justify-end bg-[#18181A] border-t border-[#242424] flex flex-col">
                  <div className="flex items-center justify-end w-full">
                    <div className="flex items-center justify-between w-full gap-2">
                      <Button
                        onClick={() =>
                          setModalStatus({
                            ...modalStatus,
                            replaceDeploymentModal: true,
                            confirmReplaceDeploymentModal: false,
                          })
                        }
                        className="bg-[#FFFFFF1A] text-white hover:bg-[#FFFFFF20] rounded-full px-4 py-2 text-sm flex items-center gap-2"
                      >
                        <img
                          src={ArrowLeft}
                          alt="Arrow Left"
                          className="w-5 h-5"
                        />
                        <span className="text-[#FFFFFF99]">Go Back</span>
                      </Button>
                      <button
                        type="button"
                        onClick={handleReplacement}
                        className="bg-[#80FFF9] flex px-3 py-2 md:px-6 md:py-3 rounded-[28px]"
                      >
                        <div className="flex items-center text-[14px] md:text-[16px] gap-2 text-[#0E0E0F] font-semibold">
                          Replace Deployment
                          <img
                            src={ReplaceIconBlack}
                            alt="Replace"
                            className="w-6 h-6"
                          />
                        </div>
                      </button>
                    </div>
                  </div>
                </div>
              </ModalOverlay>

              <BuildLogsModal
                isOpen={modalStatus.buildLogsModal}
                onClose={() =>
                  setModalStatus({
                    ...modalStatus,
                    buildLogsModal: false,
                  })
                }
                onShareLogs={(logs) => {
                  onShareLogs && onShareLogs(logs);
                }}
                deployLogs={deployLogs}
              />

              {replaceDeployments.length > 0 &&
                !loading && deployStatus != "running" &&
                latestRunStatus != "running" && deployStatus === "not_deployed" && (
                  <motion.div
                    className="p-3 md:pt-0 md:p-6 md:pb-5"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{
                      type: "spring",
                      stiffness: 300,
                      damping: 30,
                    }}
                  >
                    <div className="bg-[#0F0F10] p-5 border border-[#242424] rounded-[12px] flex flex-col gap-4  md:gap-[24px]">
                      <div className="flex flex-col gap-4">
                        <div className="flex items-center justify-between w-full">
                          <span className="text-[#FFFFFFCC] text-[14px] md:text-base font-medium font-['Inter']">
                            <img
                              src={ReplaceIconGrey}
                              alt="Replace"
                              className="inline-block w-6 h-6 mr-2"
                            />
                            Replace Exisiting Deployment
                          </span>
                          <div className="flex items-center gap-2 bg-[#2EE5721A] rounded-[8px] p-[6px] pr-[12px]">
                            <PulseDot
                              color="#2EE572"
                              size={16}
                              innerSize={8}
                              animate={true}
                              className=""
                            />
                            <span className="text-[#2EE572] text-nowrap font-medium text-[12px] md:text-[14px] font-['Inter']">
                              {replaceDeployments?.length} Live Apps
                            </span>
                          </div>
                        </div>

                        <span className="text-[#737780] text-[13px]  md:text-base font-['Inter']">
                          Update one of your current deployments with a new
                          application. Your existing app will be replaced and
                          there’ll be no additional charges.
                        </span>
                      </div>

                      <button
                        type="button"
                        title="Replace Existing Deployment"
                        className="bg-[#80FFF914] w-full flex items-center justify-center gap-2 p-3 rounded-[10px]"
                        onClick={() => {
                          setModalStatus({
                            ...modalStatus,
                            replaceDeploymentModal: true,
                          });
                          // Fetch fresh replace deployments when opening modal
                          fetchReplaceDeployments();
                        }}
                      >
                        <span className="text-[#80FFF9] text-[14px] md:text-base">
                          Replace Deployment
                        </span>
                        <img
                          src={ReplaceIcon}
                          alt="Replace"
                          className="w-5 h-5 md:w-6 md:h-6"
                        />
                      </button>
                    </div>
                  </motion.div>
                )}

              {/* Env Variables Section */}
              {deployStatus !== "running" &&
                latestRunStatus !== "running" &&
                !loading && deployStatus != "not_deployed" &&
                !isDeploymentTransitioning && (
                  <motion.div
                    className="px-3 pt-0 md:px-5"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{
                      type: "spring",
                      stiffness: 300,
                      damping: 30,
                    }}
                    key="env-variables-section"
                  >
                    <EnvVariablesSection
                      envs={envValues}
                      onSyncStatusChange={setEnvSyncedWithProduction}
                      saveEnvironmentVariables={saveEnvironmentVariables}
                      deployStatus={deployStatus}
                      jobId={jobId}
                      updateEnvironmentVariables={updateEnvironmentVariables}
                    />
                  </motion.div>
                )}


            </div>

            {/* Footer */}

            {/* Footer for Running Part */}
            {(latestRunStatus === "running" || deployStatus === "running") &&
              !isDeploymentTransitioning && (
                <div className="absolute bottom-0 md:min-h-[96px] max-h-[96px] left-0 right-0 bg-[#111112] border-t border-[#242424] md:px-8 p-3  md:py-9 flex items-center justify-start w-full">
                  <img
                    src={animatedSpinner}
                    alt="Deploy Time"
                    className="w-6 h-6 mr-2 grayscale"
                  />
                  <span className="text-[#FFFFFF] text-[14px] md:text-[16px] opacity-50">
                    Deployment started {elapsedTime} ago...
                  </span>
                </div>
              )}

            {/* Footer for Transitioning State */}
            {isDeploymentTransitioning && (
              <div className="absolute bottom-0 min-h-[96px] max-h-[96px] left-0 right-0 bg-[#111112] border-t border-[#242424] px-8 py-9 flex items-center justify-start w-full">
                <img
                  src={animatedSpinner}
                  alt="Deploy Time"
                  className="w-6 h-6 mr-2"
                />
                <span className="text-[#FFFFFF] opacity-50">
                  Preparing deployment...
                </span>
              </div>
            )}

            {/* Footer for Success and Failed Part */}
            {(deployStatus === "success" || deployStatus === "failed") &&
              !(latestRunStatus === "running") &&
              !isDeploymentTransitioning && (
                <DeploymentFooter
                  className="absolute bottom-0 left-0 right-0 flex items-center justify-between w-full"
                  showEnvWarning={!envSyncedWithProduction}
                >
                  <div className=" items-center gap-3 text-[#737780] hidden md:flex text-sm md:max-w-[250px] font-medium ]">
                    <img
                      src={DeployTime}
                      alt="Deploy Time"
                      className="w-8 h-8"
                    />
                    <span className=" text-[12px] md:text-[14px] font-['Inter']">
                      Deployment usually takes around 5-7 mins.
                    </span>
                  </div>
                  <div className="flex items-center justify-end w-full gap-4">
                    <button
                      type="button"
                      onClick={() => {
                        fetchReplaceDeployments();
                        handleShutDown();
                      }}
                      className="bg-[#272729] text-white hover:bg-[#272729]/80 rounded-full md:px-[20px] px-3 py-2 md:py-3 text-sm flex items-center gap-1"
                    >
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-[12px]  md:text-[16px]">
                          Shut Down
                        </span>
                        <img
                          src={DustBin}
                          alt="Dust Bin"
                          className="hidden w-6 h-6 md:block"
                        />
                      </div>
                    </button>
                    <button
                      type="button"
                      onClick={
                        latestSuccessfulDeployment
                          ? handleReDeploy
                          : handleDeployClick
                      }
                      className="flex items-center gap-1 px-3 py-2 text-sm text-black bg-white rounded-full md:px-5 md:py-3 hover:bg-white/80"
                      // @ts-ignore
                      disabled={
                        loading || localLoading || isDeploymentTransitioning
                      }
                    >
                      {loading || localLoading || isDeploymentTransitioning ? (
                        <>
                          <Loader2 className="w-6 h-6 mr-1 animate-spin" />
                          <span>Loading...</span>
                        </>
                      ) : (
                        <div className="flex items-center gap-2">
                          <span className="font-semibold text-[12px]  md:text-[16px]">
                            {latestSuccessfulDeployment
                              ? "Re-Deploy"
                              : "Start Deployment"}
                          </span>
                          <img
                            src={DeployArrowDark}
                            alt="Deploy"
                            className="hidden w-6 h-6 md:block"
                          />
                        </div>
                      )}
                    </button>
                  </div>
                </DeploymentFooter>
              )}
          </div>
    </div>
  );
}
