import { Button } from "@/components/ui/button";
import {  X } from "lucide-react";
import { useMemo, useState, useEffect, useRef } from "react";
import { ChatInput } from "../ChatInput";
import { ResponseImageData } from "@/types/message";
import { MessageItem } from "../MessageItem";
import { AgentMessageItem } from "../AgentMessageItem";
import { Chunk } from "@/types/chunk";
import { getSubagentName } from "@/utils/commonUtils";
import { cn } from "@/lib/utils";

interface SubagentMessagesPanelProps {
  isOpen: boolean;
  onClose: () => void;
  messages: {
    id: string;
    role: "user" | "assistant";
    content: string;
    timestamp: string;
    agent_name?: string;
    action?: string;
    observation?: string;
    env_success?: boolean;
    base64_image_list?: ResponseImageData[];
    function_name?: string | null;
  }[];
  onSubmit?: (message: string, images?: ResponseImageData[]) => void;
  onPause?: ({origin}: {origin?: "MainInput" | "SubagentButton"}) => void;
  agentState?: {
    agent_running: boolean;
    job_running: boolean;
  } | null;
  containerId?: string | null;
  jobDetails?: {
    job_id: string;
    traj_path: string;
    createdBy: string;
  };
  selectedAgent?: string;
  subagentName?: string;
  isSubagentActive?: boolean;
  isCloudFlow?: boolean;
  pauseWasClicked?: boolean;
  isPauseLoading?: boolean;
  // Add any other props you need
  togglePanel?: ({ panelName, value }: {
    panelName: "showLogsPanel" | "showSubagentPanel" | "showInfoPanel" | "showUrlPreviewPanel";
    value?: boolean | null;
  }) => void;
  userInitials?: string;
  hideImportantActions?: boolean;
  searchState: {
    isActive: boolean;
    query: string;
  };
  searchHighlights: Array<{ startIndex: number; endIndex: number }>;
  isMatchingMessage: boolean;
  currentChunk?: Chunk | null;
  podIsPaused?: boolean;
}

export function SubagentMessagesPanel({
  isOpen,
  onClose,
  messages,
  onSubmit,
  onPause,
  agentState,
  containerId,
  jobDetails,
  togglePanel,
  selectedAgent,
  hideImportantActions = false,
  userInitials,
  subagentName = "Subagent",
  isSubagentActive = false,
  isCloudFlow = false,
  pauseWasClicked = false,
  isPauseLoading = false,
  searchHighlights,
  isMatchingMessage,
  searchState,
  currentChunk,
  podIsPaused = false,
}: SubagentMessagesPanelProps) {
  const [expandedMessages, setExpandedMessages] = useState<Set<string>>(new Set());
  const [isSubagentWaiting, setIsSubagentWaiting] = useState(false);
  const [localMessages, setLocalMessages] = useState<typeof messages>([]);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [wasAtBottom, setWasAtBottom] = useState(true);

  // Update local messages whenever the prop messages change
  useEffect(() => {
    setLocalMessages(messages);
  }, [messages]);

  // Memoize sorted messages to prevent unnecessary re-renders
  const sortedMessages = useMemo(() => {
    return [...localMessages].sort((a, b) => {
      const timeA = new Date(a.timestamp).getTime();
      const timeB = new Date(b.timestamp).getTime();
      return timeA - timeB;
    });
  }, [localMessages]);

  // Handle scroll events to track if we're at the bottom
  const handleScroll = () => {
    if (messagesContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
      const isAtBottom = Math.abs(scrollHeight - scrollTop - clientHeight) < 10;
      setWasAtBottom(isAtBottom);
    }
  };

  // Auto-scroll to bottom when new messages arrive if we were already at the bottom
  useEffect(() => {
    if (messagesContainerRef.current && wasAtBottom) {
      // Use setTimeout to ensure the DOM has updated before scrolling
      setTimeout(() => {
        if (messagesContainerRef.current) {
          messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
        }
      }, 0);
    }
  }, [sortedMessages, currentChunk]);

  // Scroll to bottom when the panel is opened
  useEffect(() => {
    if (isOpen && messagesContainerRef.current) {
      // Use setTimeout to ensure the DOM has updated before scrolling
      setTimeout(() => {
        if (messagesContainerRef.current) {
          messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
        }
      }, 0);
    }
  }, [isOpen]);

  // Get the agent name from the last message in trajectory
  const currentAgentName = useMemo(() => {
    if (sortedMessages.length > 0) {
      return sortedMessages[sortedMessages.length - 1].agent_name;
    }
    return undefined;
  }, [sortedMessages]);

  // //console.log(sortedMessages, 'sortedMessages');

  // Detect if the subagent is waiting for a response or has finished
  useEffect(() => {
    if (sortedMessages.length > 0) {
      const lastMessage = sortedMessages[sortedMessages.length - 1];
      // Check if the last message has action "finish"
      if (lastMessage.action === "finish") {
        setIsSubagentWaiting(false);
      }
      // If the last message is from the assistant, has no action/observation, it's waiting for a response
      else if (!lastMessage.action && !lastMessage.observation) {
        setIsSubagentWaiting(true);
      } else {
        setIsSubagentWaiting(false);
      }
    } else {
      setIsSubagentWaiting(false);
    }
  }, [sortedMessages]);

  // Check if the subagent has finished (last message has action "finish")
  const isSubagentFinished = useMemo(() => {
    if (sortedMessages.length > 0) {
      const lastMessage = sortedMessages[sortedMessages.length - 1];
      return lastMessage.action === "finish";
    }
    return false;
  }, [sortedMessages]);

  // Handle message submission with local state update
  const handleSubmit = (message: string, images?: ResponseImageData[]) => {
    if (!message.trim() || !onSubmit) return;

    // Create a new user message
    const messageId = `local-user-${Date.now()}`;
    const newUserMessage = {
      id: messageId,
      role: "user" as const,
      content: message,
      timestamp: new Date().toISOString(),
      agent_name: currentAgentName,
      base64_image_list: images,
      request_id: messageId
    };

    // Add the message to local state immediately
    setLocalMessages(prev => [...prev, newUserMessage]);

    // Then send it to the server
    onSubmit(message, images);

    // Scroll to bottom after sending a message
    setTimeout(() => {
      if (messagesContainerRef.current) {
        messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
      }
    }, 0);
  };

  return (
    <div
      className={cn(`w-full h-[calc(100%-3.5rem)] bg-[#0F0F10]  max-md:absolute max-md:inset-0 z-[49]`, isOpen ? "block":"hidden")}
    >
      <div className="flex flex-col h-full">
        <div className="p-4 md:px-6 md:py-5 bg-[#181818] md:bg-transparent flex items-center justify-between border-b border-[#242424]/60">
          <div className="text-[#939399] font-['Brockmann'] flex items-center gap-2 text-[15px]  md:text-[18px] font-medium leading-[24px] capitalize">
            {getSubagentName(subagentName)} ({sortedMessages.length})
          </div>

          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="w-8 h-8 hover:bg-transparent"
          >
            <X
              className="w-6 h-6 text-white"
            />
          </Button>
        </div>

        <div
          ref={messagesContainerRef}
          className="flex-1 p-6 overflow-x-hidden overflow-y-auto"
          onScroll={handleScroll}
        >
          {sortedMessages.map((message) => {
            return <div style={{ width: "100%" }} key={message.id}>

              {
                message.role === "assistant" && message.action &&
                <AgentMessageItem
                  currentChunk={""}
                  togglePanel={togglePanel as any}
                  message={message as any}
                  userInitials={userInitials || ''}
                  onMergeToLocal={() => { }}
                  onShowSubagentMessages={() => { }}
                  onSubagentClick={() => { }}
                  handleOpenVsCode={() => { }}
                  handleRollback={() => { }}
                  hideImportantActions={hideImportantActions}
                  isCloudFlow={isCloudFlow}
                  searchActive={searchState.isActive}
                  searchQuery={searchState.query}
                  searchHighlights={searchHighlights}
                  isMatchingMessage={isMatchingMessage}
                  isSubagent={true}
                  handleAddToken={() => { }}
                  acc_cost={0}
                  max_budget={0}
                  selectedMessageId={undefined}
                />
              }
              {
                message.role === "user" &&
                <MessageItem
                  key={message.id}
                  message={message}
                  isSubagent={true}
                />
              }
            </div>
          })}
        </div>

        {/* Add ChatInput at the bottom if subagent hasn't finished */}
        {!isSubagentFinished && (
          <div className="p-2 md:p-4 border-t border-[#242424]/60">
            <ChatInput
              buildMode={"build"}
              onSubmit={handleSubmit}
              placeholder={`Message ${getSubagentName(subagentName) || 'Subagent'}`}
              hideStatus={true}
              isDisabled={podIsPaused}
              hideTokens={true}
              showImages={true}
              isCloudFlow={isCloudFlow}
              agentName={currentAgentName}
              agentState={agentState}
              containerId={containerId}
              jobDetails={jobDetails}
              isSubagentActive={isSubagentActive || isSubagentWaiting}
              onPause={onPause}
              onAddToken={() => { }} // Empty function as it's hidden
              pauseWasClicked={pauseWasClicked}
              isPauseLoading={isPauseLoading}
              onImagesChange={(images) => { }}
              fromSubagentPanel={true}
            />
          </div>
        )}
      </div>
    </div>
  );
}
