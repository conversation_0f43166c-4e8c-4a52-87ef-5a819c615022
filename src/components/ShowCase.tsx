import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "motion/react";
import featuredIcon from "@/assets/featured.svg";
import { cn } from "@/lib/utils";
import ShowcaseCard from "@/components/ShowcaseCard";
import ShowCaseArrowSVG from "@/assets/showcaseArrow.svg";
import { AnimatedShinyText } from "./ui/animated-shiny-text";
import { agent<PERSON>pi, JobResponse } from "@/services/agentApi";
import { useTabState } from "./TabBar";
import AIAppsSVGDark from "@/assets/cat/ai_apps_black.svg"
import AIAppsSVGLight from "@/assets/cat/ai_apps_white.svg"
import LandingPageDark from "@/assets/cat/landing_page_black.svg"
import LandingPageLight from "@/assets/cat/landing_page_white.svg"
import MircroSassDark from "@/assets/cat/micro_saas_black.svg"
import MircroSassLight from "@/assets/cat/micro_saas_white.svg"
import PlayLight from "@/assets/cat/play_white.svg"
import PlayDark from "@/assets/cat/play_black.svg"


interface IShowCaseData {
    category_name: string;
    apps: {
        name: string;
        description: string;
        image: string;
        job_id: string;
        preview_url: string;
    }[];
}

const ShowCase = ({ showCaseData }: {
    showCaseData: IShowCaseData[];
}) => {

    // Hooks
    const { setTabs, updateTabState, setActiveTab } = useTabState();
    const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);

    // States
    const [activeCategory, setActiveCategory] = useState(showCaseData[0]?.category_name);

    useEffect(() => {
        setActiveCategory(showCaseData[0]?.category_name);
    }, [showCaseData]);

    const filteredShowcase = showCaseData.filter((item) =>
        item.category_name === activeCategory
    )[0]?.apps || [];

    const openShowCase = async (jobId: string, previewUrl: string) => {
        try {
            const response = await agentApi.getJob(jobId.trim());
            const job = response.data;
            handleJobClick(job, previewUrl);

        } catch (error) {
            console.error("Error fetching job details:", error);
        }
    }

    const handleJobClick = (job: JobResponse, previewUrl: string) => {

        // ipc.openExternal(previewUrl);

        if (!job.payload.task) {
            console.error("No task data available");
            return;
        }

        const containerId = job.payload.container_id || job.id;
        if (!containerId) {
            console.error("No container ID available");
            return;
        }

        // Generate a unique tab ID for this chat
        const newTabId = `tab-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        // Construct the full trajectory path
        const trajPath = `/root/runs/${job.id}/trajectory.json`;

        const tabState = {
            containerId: job.payload.container_id || job.id,
            initial_commit_id: job.payload.initial_commit_id,
            task: job.payload.task,
            jobId: job.id,
            trajPath,
            tabId: newTabId,
            fromJobList: true,
            isCloudFlow: job.payload?.is_cloud,
            clientRefId: job.client_ref_id,
            modelName: job.payload.model_name,
            promptName: job.payload.prompt_name,
            promptVersion: job.payload.prompt_version,
            costLimit: job.payload.per_instance_cost_limit,
            agentName: job.payload.agent_name,
            portMapping: job.payload.portMapping,
            previewUrl: previewUrl,
            showCase: true
        };

        // Create new tab
        setTabs((prevTabs) => [
            ...prevTabs,
            {
                id: newTabId,
                title: `${job.payload.task}`,
                path: "/chat",
                state: tabState,
            },
        ]);

        // Update tab state
        updateTabState(newTabId, tabState);

        // Set active tab and navigate
        setActiveTab(newTabId);
    };

    const getIcon = (category: string, isActive: boolean, isHovered: boolean) => {
        // Use dark icon when active or hovered, light icon otherwise
        const useDarkIcon = isActive || isHovered;

        switch (category) {
            case "AI Apps":
                return useDarkIcon ? AIAppsSVGDark : AIAppsSVGLight;
            case "Landing":
                return useDarkIcon ? LandingPageDark : LandingPageLight;
            case "Digital Sidekicks":
                return useDarkIcon ? MircroSassDark : MircroSassLight;
            case "Hack & Play":
                return useDarkIcon ? PlayDark : PlayLight;
            default:
                return featuredIcon;
        }
    }

    return (
        <section className="flex flex-col md:pb-20 md:gap-[16px] items-center">
            <div className="flex flex-wrap justify-between gap-2 p-6 pt-4 mx-auto md:gap-4 md:w-full md:max-w-5xl">
                {showCaseData.map((btn, index) => {
                    const isActive = activeCategory === btn.category_name;
                    return (
                        <button
                            key={btn.category_name + index}
                            onClick={() => setActiveCategory(btn.category_name)}
                            onMouseEnter={() => setHoveredCategory(btn.category_name)}
                            onMouseLeave={() => setHoveredCategory(null)}
                            id={`showcase-category-${btn.category_name}`}
                            className={cn(
                                "md:h-[48px] gap-1  md:gap-[6px] font-brockmann flex items-center justify-center text-[24px] flex-grow flex-1 transition-all duration-300 ease-in-out px-4 py-2  whitespace-nowrap rounded-full ",
                                isActive
                                    ? "bg-white text-[#0F0F10] font-semibold hover:bg-white/90"
                                    : "bg-[#1D1D1F] text-white font-medium hover:bg-[#2D2D2F]"
                            )}
                        >
                            <img
                                src={getIcon(btn.category_name, isActive, false)}
                                alt={`${btn.category_name} Icon`}
                                className={isActive ? "min-w-5 min-h-5" : "min-w-5 min-h-5 opacity-60"}
                            />
                            <span className={cn(
                                "text-[14px] md:text-[16px] font-brockmann capitalize",
                                isActive
                                    ? "text-[#0F0F10] font-semibold"
                                    : "text-white font-medium opacity-60"
                            )}>
                                {btn.category_name}
                            </span>
                        </button>
                    );
                })}
            </div>

            <motion.div
                className="w-full px-4"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.2 }}
            >
                <AnimatePresence mode="wait">
                    <motion.div
                        key={activeCategory}
                        className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.2 }}
                    >
                        {filteredShowcase.map((data, index) => (
                            <ShowcaseCard
                                key={data.job_id + index}
                                title={data.name}
                                description={data.description}
                                imageLink={data.image || "https://s3-alpha-sig.figma.com/img/8133/f6ec/448fb87c3cb4871ded0d5a981369605f?Expires=1743379200&Key-Pair-Id=APKAQ4GOSFWCW27IBOMQ&Signature=d9A8rwjeZQrHQOGWyQZNxi9qXlHsOfx4C8QMqY~KtEh16KQQLyi8BfqcXdp334IxRyc1G0Fw1xYsaxnHlTMGB~4-QRGatbr7f~j1BSsG1hKiaFNLXPo0BarLiIAKQNWSNV3rFdkIZct2dTSGRlRY5o3bR2PeVoNXxDWA~lpf9kFgehmhYLi4IwMFr0yqvSvzjOqnYIRYzml6xCVE325kQxp-8AK0GTeU1ns9mu87eVeKwXKniigyGebSSU1XWd2jeIOLL2zytjKx3eup8EnG4JHrHr1BZSBNP84pMZQeQqpWii52LAhdkx-qUa9lsmFjAgsV5P4nl~zp7DeO4jI0lw__"}
                                hrefLink={data.preview_url}
                                handleOnClick={() => openShowCase(data.job_id, data.preview_url)}
                                job_id={data.job_id}
                                className="h-[280px] w-full"
                            />
                        ))}
                    </motion.div>
                </AnimatePresence>
            </motion.div>


            <motion.div
                className="mt-8"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{
                    type: "spring",
                    stiffness: 100,
                    damping: 10,
                    delay: 0.4
                }}
            >
                <a className="bg-[#131314] group rounded-[40px] hover:border-white/40 mt-4 py-3 px-5 border-[1px] w-fit border-white/20 hover:bg-[#1c1c1f]" href="https://emergent.sh/showcase" target="_blank" rel="noreferrer noopener">
                    <AnimatedShinyText className="gap-2 text-[#CCCCCC] font-brockmann font-medium inline-flex items-center justify-center transition ease-out hover:text-neutral-600 hover:duration-300 hover:dark:text-neutral-400 text-sm">
                        Visit Our Showcase <img title="Arrow" src={ShowCaseArrowSVG} className="w-5 h-5 opacity-35 group-hover:opacity-100" alt="Arrow" />
                    </AnimatedShinyText>
                </a>
            </motion.div>
        </section>
    );
};

export default ShowCase;