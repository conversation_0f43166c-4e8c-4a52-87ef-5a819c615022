import React, { memo } from 'react';
import ReactMarkdown, { Components } from 'react-markdown';
import { cn } from '@/lib/utils';

export interface StyledMarkdownProps {
  children: string;
  className?: string;
  variant?: 'default' | 'compact' | 'minimal';
  customComponents?: Partial<Components>;
}

const StyledMarkdownComponent: React.FC<StyledMarkdownProps> = ({
  children,
  className,
  variant = 'default',
  customComponents = {},
}) => {
  // Base styles that apply to all variants
  const baseStyles = "selection:text-[#66EAFF] selection:bg-[#66EAFF] selection:bg-opacity-10 font-['Inter'] max-w-3xl select-text";

  // Variant-specific styles
  const variantStyles = {
    default: "p-0 text-[#dcdce5] font-['Inter'] text-[16px] leading-[28px] font-normal max-w-4xl",
    compact: "p-0 text-[#dcdce5] font-['Inter'] text-[14px] leading-[24px] font-normal max-w-4xl",
    minimal: "p-0 text-[#dcdce5] font-['Inter'] text-[14px] leading-[20px] font-normal max-w-4xl",
  };

  // Default component mappings
  const defaultComponents: Components = {
    // Paragraph styling
    p: ({ ...props }) => (
      <p className="my-4 font-['Inter']" {...props} />
    ),

    // Heading hierarchy with distinct styling
    h1: ({ ...props }) => (
      <h1
        className="mt-8 mb-4 text-[28px] font-bold tracking-tight text-white font-['Inter']"
        {...props}
      />
    ),
    h2: ({ ...props }) => (
      <h2
        className="mt-6 mb-3 text-2xl font-semibold tracking-tight text-white/90 font-['Inter']"
        {...props}
      />
    ),
    h3: ({ ...props }) => (
      <h3
        className="mt-5 mb-2 text-xl font-bold text-white/80 font-['Inter']"
        {...props}
      />
    ),
    h4: ({ ...props }) => (
      <h4
        className="mt-4 mb-2 text-lg font-semibold text-white/70 font-['Inter']"
        {...props}
      />
    ),
    h5: ({ ...props }) => (
      <h5
        className="mt-3 mb-1 text-base font-medium text-white/60 font-['Inter']"
        {...props}
      />
    ),
    h6: ({ ...props }) => (
      <h6
        className="mt-2 mb-1 text-sm font-medium tracking-wider uppercase text-white/50 font-['Inter']"
        {...props}
      />
    ),

    // List styling
    ul: ({ ...props }) => (
      <ul className="pl-4 my-3 space-y-1 list-disc font-['Inter']" {...props} />
    ),
    ol: ({ ...props }) => (
      <ol className="pl-4 my-3 space-y-1 list-decimal font-['Inter']" {...props} />
    ),
    li: ({ ...props }) => <li className="pl-1 ml-4 font-['Inter']" {...props} />,

    // Blockquote styling
    blockquote: ({ ...props }) => (
      <blockquote
        className="pl-4 my-4 italic border-l-4 border-blue-500/40 text-white/70 font-['Inter']"
        {...props}
      />
    ),

    // Code blocks and inline code
    code: ({ inline, className, children, ...props }: { 
      inline?: boolean; 
      className?: string;
      children?: React.ReactNode;
      [key: string]: any 
    }) => {
      const content = String(children || '');
      
      // Multiple ways to detect inline code:
      // 1. Explicit inline prop from ReactMarkdown
      // 2. No className (fenced blocks usually have language- classes)
      // 3. No newlines in short content
      // 4. Check if parent is a <pre> tag (block code is wrapped in <pre><code>)
      const isInline = inline === true || 
                      (inline !== false && 
                       !className?.startsWith('language-') && 
                       !content.includes('\n') && 
                       content.length < 200);
      if (isInline) {
        return (
          <span
            className=" px-1.5 py-0.5 rounded-[6px] text-[12px] font-jetbrains border border-[#80fff920]  bg-[#80fff910] text-[#80fff9] whitespace-nowrap inline"
            {...props}
          >
            {children}
          </span>
        );
      }
      
      // Block code (fenced code blocks)
      return (
        <span
          className="block p-4 overflow-x-auto text-sm rounded-lg bg-white/5 text-[#dcdce5] font-jetbrains"
          {...props}
        >
          {children}
        </span>
      );
    },

    pre: ({ ...props }) => (
      <pre
        className="p-0 my-4 overflow-hidden bg-transparent rounded-md"
        {...props}
      />
    ),

    // Link styling
    a: ({ ...props }) => (
      <a
        className="text-blue-400 transition-colors duration-200 hover:text-blue-300 hover:underline underline-offset-2 font-['Inter']"
        {...props}
      />
    ),

    // Table styling
    table: ({ ...props }) => (
      <table className="w-full my-4 border-collapse font-['Inter']" {...props} />
    ),
    thead: ({ ...props }) => (
      <thead className="bg-gray-800/40 font-['Inter']" {...props} />
    ),
    tbody: ({ ...props }) => (
      <tbody className="divide-y divide-gray-800/60 font-['Inter']" {...props} />
    ),
    tr: ({ ...props }) => (
      <tr
        className="transition-colors duration-150 hover:bg-gray-800/20 font-['Inter']"
        {...props}
      />
    ),
    th: ({ ...props }) => (
      <th
        className="px-3 py-2 font-semibold text-left text-white/80 font-['Inter']"
        {...props}
      />
    ),
    td: ({ ...props }) => <td className="px-3 py-2 font-['Inter']" {...props} />,

    // Horizontal rule
    hr: ({ ...props }) => (
      <hr className="my-6 border-t border-gray-700/50 font-['Inter']" {...props} />
    ),

    // Strong and emphasis
    strong: ({ ...props }) => (
      <strong className="font-bold text-white font-['Inter']" {...props} />
    ),
    em: ({ ...props }) => (
      <em className="italic text-white/90 font-['Inter']" {...props} />
    ),

    // Image styling
    img: ({ ...props }) => (
      <img
        className="h-auto max-w-full my-4 rounded-lg shadow-md shadow-black/20"
        {...props}
        loading="lazy"
      />
    ),

    // Definition list
    dl: ({ ...props }) => (
      <dl className="my-4 space-y-2 font-['Inter']" {...props} />
    ),
    dt: ({ ...props }) => (
      <dt className="font-medium text-white/90 font-['Inter']" {...props} />
    ),
    dd: ({ ...props }) => (
      <dd className="ml-4 text-white/70 font-['Inter']" {...props} />
    ),

    // Miscellaneous elements
    details: ({ ...props }) => (
      <details
        className="my-3 overflow-hidden font-['Inter'] border rounded-md border-gray-700/50"
        {...props}
      />
    ),
    summary: ({ ...props }) => (
      <summary
        className="px-4 py-2 transition-colors font-['Inter'] duration-200 cursor-pointer bg-gray-800/30 hover:bg-gray-800/50"
        {...props}
      />
    ),

    // Superscript and subscript
    sup: ({ ...props }) => (
      <sup className="text-xs align-super font-['Inter']" {...props} />
    ),
    sub: ({ ...props }) => (
      <sub className="text-xs align-sub font-['Inter']" {...props} />
    ),
  };

  const components = { ...defaultComponents, ...customComponents };

  return (
    <ReactMarkdown
      className={cn(baseStyles, variantStyles[variant], className, "max-w-[50rem]")}
      components={components}
    >
      {children}
    </ReactMarkdown>
  );
};

// Memoize the component to prevent unnecessary re-renders that break text selection
export const StyledMarkdown = memo(StyledMarkdownComponent, (prevProps, nextProps) => {
  // Only re-render if the content actually changes
  return (
    prevProps.children === nextProps.children &&
    prevProps.variant === nextProps.variant &&
    prevProps.className === nextProps.className
  );
});

export const createStyledMarkdown = (defaultProps: Partial<StyledMarkdownProps>) => {
  return (props: Partial<StyledMarkdownProps> & { children: string }) => (
    <StyledMarkdown {...defaultProps} {...props} />
  );
};

export default StyledMarkdown;
