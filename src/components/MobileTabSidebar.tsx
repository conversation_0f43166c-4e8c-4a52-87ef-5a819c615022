import React, { useCallback, useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { ITab, useTabState } from "./TabBar";
import { agentApi } from "@/services/agentApi";
import { useIsEmergentUser } from "@/hooks/useIsEmergentUser";
import { Plus, X } from "lucide-react";
import MenuBar from "./MenuBar";
import { useGitHub } from "@/hooks/useGitHubAPI";
import { githubConfig } from "@/config";
import { useAuth } from "@/contexts";
import Logo from "@/assets/logo/logo.svg";
import GlobalWhite from "@/assets/globe_white.svg"
import MobileDeployedAppsModal from "./MobileDeployedAppsModal";

interface Job {
  id: string;
  state: string;
  payload: {
    task?: string;
  };
}

interface Deployment {
  id: string;
  app_name: string;
  deployStatus: string;
  deployUrl: string;
  custom_domain: string;
  latest_run: {
    status: string;
    updated_at: string | null;
  };
  description: string;
}

interface MobileTabSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  tabs: ITab[];
  activeTab: string;
  onTabClick: (tab: ITab) => void;
  onTabClose: (e: React.MouseEvent, tab: ITab) => void;
  onNewTab: () => void;
}

const MobileTabSidebar: React.FC<MobileTabSidebarProps> = ({
  isOpen,
  onClose,
  tabs,
  activeTab,
  onTabClick,
  onTabClose,
  onNewTab,
}) => {
  const [recentTasks, setRecentTasks] = useState<Job[]>([]);
  const [deployedApps, setDeployedApps] = useState<Deployment[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isDeployedAppsModalOpen, setIsDeployedAppsModalOpen] = useState(false);
  const isEmergentUser = useIsEmergentUser();
  // Fetch recent tasks and deployed apps when the sidebar is opened
  useEffect(() => {
    if (isOpen) {
      fetchRecentTasks();
      if (isEmergentUser) {
        fetchDeployedApps();
      }
    }
  }, [isOpen, isEmergentUser]);

  const { setTabs, setActiveTab, updateTabState, state } = useTabState();

  // Fetch recent tasks
  const fetchRecentTasks = async () => {
    try {
      setIsLoading(true);
      const response = await agentApi.getJobs();
      setRecentTasks(response.data); // Limit to 10 recent tasks
    } catch (error) {
      console.error("Error fetching recent tasks:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch deployed apps
  const fetchDeployedApps = async () => {
    try {
      setIsLoading(true);
      const response = await agentApi.getDeployments();
      if (!("error" in response)) {
        const mappedDeployments = response.map((deployment: any) => ({
          id: deployment.job_id,
          app_name: deployment.app_name || "Unnamed App",
          deployStatus: deployment.status,
          deployUrl: deployment.deployed_url || "",
          custom_domain: deployment.custom_domain || "",
          latest_run: {
            status: deployment.status,
            updated_at: deployment.deployed_at || null,
          },
          description: deployment.task || "No task available",
          deployment_id: deployment.deployment_id,
        }));

        const sortedDeployments = mappedDeployments.sort((a: any, b: any) => {
          const timeA = new Date(a.latest_run.updated_at).getTime();
          const timeB = new Date(b.latest_run.updated_at).getTime();
          return timeB - timeA;
        });

        setDeployedApps(sortedDeployments.slice(0, 5));
      }
    } catch (error) {
      console.error("Error fetching deployed apps:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle job click
  const handleJobClick = (job: Job) => {
    if (!job.payload.task) {
      console.error("No task data available");
      return;
    }

    const containerId = job.payload.container_id || job.id;
    if (!containerId) {
      console.error("No container ID available");
      return;
    }

    // Generate a unique tab ID for this chat
    const newTabId = `tab-${Date.now()}-${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    // Construct the full trajectory path
    const trajPath = `/root/runs/${job.id}/trajectory.json`;

    const tabState = {
      containerId: job.payload.container_id || job.id,
      initial_commit_id: job.payload.initial_commit_id,
      task: job.payload.task,
      jobId: job.id,
      trajPath,
      tabId: newTabId,
      fromJobList: true,
      isCloudFlow: job.payload?.is_cloud,
      clientRefId: job.client_ref_id,
      modelName: job.payload.model_name,
      promptName: job.payload.prompt_name,
      promptVersion: job.payload.prompt_version,
      costLimit: job.payload.per_instance_cost_limit,
      agentName: job.payload.agent_name,
      portMapping: job.payload.portMapping,
      created_by: job.created_by,
    };

    // Create new tab
    setTabs((prevTabs) => [
      ...prevTabs,
      {
        id: newTabId,
        title: `${job.payload.task}`,
        path: "/chat",
        state: tabState,
      },
    ]);

    // Update tab state
    updateTabState(newTabId, tabState);

    // Set active tab and navigate
    setActiveTab(newTabId);

    setIsDeployedAppsModalOpen(false);

    onClose();
  };

  // Handle backdrop click to close the sidebar
  const handleBackdropClick = useCallback(() => {
    onClose();
  }, [onClose]);

  // Prevent clicks inside the sidebar from closing it
  const handleSidebarClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
  }, []);

  const {
    isConnected: isGitHubConnected,
    checkGitHubConnection,
    setIsConnected,
  } = useGitHub();

  const { user, signOut, session } = useAuth();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleSignOut = async () => {
    setIsLoggingOut(true);
    setTabs((tabs) => tabs.filter((tab) => tab.id === "home"));

    setIsConnected(false);
    setActiveTab("home");

    await signOut().then(() => {
      setRecentTasks([]);
      setIsLoggingOut(false);
    });
  };

  const handleGitHubConfigClick = () => {
    const githubAppUrl = githubConfig.appUrl;
    window.open(githubAppUrl, "_blank");
  };

  useEffect(() => {
    if (session) {
      checkGitHubConnection();
    }
  }, [session]);

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop overlay */}
          <motion.div
            className="fixed inset-0 z-[999] bg-black/50 backdrop-blur-sm lg:hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            onClick={handleBackdropClick}
          />

          {/* Sidebar */}
          <motion.div
            className="fixed top-0 left-0 z-[999] flex h-full w-72 flex-col overflow-hidden border-r border-[#272729] bg-[#131314] lg:hidden"
            initial={{ x: "-100%" }}
            animate={{ x: 0 }}
            exit={{ x: "-100%" }}
            transition={{ type: "tween", duration: 0.3, ease: "easeInOut" }}
            onClick={handleSidebarClick}
          >
            {/* Header */}
            <div className="relative flex items-center justify-between w-full p-4 px-2 pr-2">
              <div className="flex items-center">
                <img
                  onClick={() => {
                    onTabClick(tabs.find((tab) => tab.id === "home")!);
                    onClose();
                  }}
                  src={Logo}
                  alt="Logo"
                  className=" h-[24px] w-[110px]"
                />
              </div>
              <button
                type="button"
                title="Close Sidebar"
                onClick={onClose}
                className="p-1 rounded-full hover:bg-white/10"
              >
                <X className="w-5 h-5 text-white" />
              </button>
            </div>

            <div className="flex flex-col gap-2 p-2 pt-3">
              <div
                className={cn(
                  "flex cursor-pointer items-center justify-between px-3 py-2 rounded-[8px]",
                  activeTab === "home" ? "bg-[#80fff9]/10" : "bg-[#80fff9]/10"
                )}
                onClick={() => {
                  onTabClick(tabs.find((tab) => tab.id === "home")!);
                  onClose();
                }}
              >
                <div className="flex items-center space-x-3 overflow-hidden">
                  <Plus className="w-4 h-4 text-[#80fff9]/90" />
                  <span
                    className={cn(
                      "truncate text-sm font-medium",
                      activeTab === "home"
                        ? "text-[#80fff9]/90"
                        : "text-[#80fff9]/70"
                    )}
                  >
                    New Task
                  </span>
                </div>
              </div>

              {user && <div
                className={cn(
                  "flex cursor-pointer items-center justify-between px-3 py-2 rounded-[8px]",
                  "bg-white/10"
                )}
                onClick={() => {
                  onClose(); // Close sidebar first
                  setTimeout(() => {
                    setIsDeployedAppsModalOpen(true); // Then open modal
                  }, 100);
                }}
              >
                <div className="flex items-center space-x-3 overflow-hidden">
                  <img src={GlobalWhite} alt="Global" className="w-4 h-4" />
                  <span
                    className={cn(
                      "truncate text-sm text-white font-medium",
                    )}
                  >
                    Deployed Apps
                  </span>
                </div>
              </div>}

              <div className="">
                {tabs.length > 2 && (
                  <span className="text-[12px] font-brockmann opacity-65 pl-1">
                    Active Tasks
                  </span>
                )}

                {/* Open Tabs Section */}

                <div className="flex flex-col pt-1 max-h-[30vh] overflow-y-scroll">
                  {tabs.map((tab) => {
                    // Skip hidden tabs
                    if (
                      localStorage.getItem("embeddedTaskTabId") === tab.id ||
                      tab.path === "/not-defined"
                    ) {
                      return null;
                    }

                    const isHomeTab = tab.id === "home";
                    const isActive = tab.id === activeTab;

                    if (isHomeTab) return null;

                    return (
                      <div
                        key={tab.id}
                        className={cn(
                          "flex cursor-pointer items-center justify-between px-3 py-2 rounded-[8px]",
                          isActive ? "bg-[#1E1E1F]" : "hover:bg-[#1E1E1F]/50"
                        )}
                        onClick={() => {
                          onTabClick(tab);
                          onClose(); // Close sidebar after selecting a tab
                        }}
                      >
                        <div className="flex items-center space-x-3 overflow-hidden">
                          {isActive && (
                            <span className="h-2 w-2 flex-shrink-0 rounded-full bg-[#00CCAF]" />
                          )}
                          <span
                            className={cn(
                              "truncate text-sm font-medium",
                              isActive ? "text-white" : "text-[#B3B3B3]"
                            )}
                          >
                            {isHomeTab ? "Home" : tab.title}
                          </span>
                        </div>

                        {!isHomeTab && (
                          <button
                            type="button"
                            title="Close Tab"
                            onClick={(e) => {
                              e.stopPropagation();
                              onTabClose(e, tab);
                            }}
                            className="p-1 rounded-full hover:bg-destructive/10"
                          >
                            <svg
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                              className="text-[#7B7B80]"
                            >
                              <path
                                d="M18 6L6 18M6 6L18 18"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          </button>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Recent Tasks Section */}
              <div className="">
                <span className="text-[12px] font-brockmann opacity-65 pl-1">
                  Recent Tasks
                </span>

                <div className="flex flex-col gap-1 pb-2 max-h-[40vh] overflow-y-scroll">
                  {isLoading ? (
                    // Loading state
                    Array(5)
                      .fill(0)
                      .map((_, index) => (
                        <div
                          key={`loading-${index}`}
                          className="border-b border-[#272729]/50 px-3 py-2"
                        >
                          <div className="mb-2 h-4 w-24 animate-pulse rounded bg-[#272729]"></div>
                          <div className="h-4 w-full animate-pulse rounded bg-[#272729]"></div>
                        </div>
                      ))
                  ) : recentTasks.length === 0 ? (
                    // Empty state
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <div className="mb-2 text-sm text-[#8F8F98]">
                        No tasks yet
                      </div>
                      <div className="max-w-[200px] text-xs text-[#92929A]">
                        Create your first task to start building
                      </div>
                    </div>
                  ) : (
                    recentTasks.map((job) => {
                      return (
                        <div
                          key={job.id}
                          className="cursor-pointer px-2 py-2 hover:bg-[#1E1E1F]/50"
                          onClick={() => handleJobClick(job)}
                        >
                          <div className="truncate text-sm text-[#C2C2CC]">
                            {job.payload.task
                              ?.slice(0, 100)
                              .replace(/"/g, "") || "No task description"}
                          </div>
                        </div>
                      );
                    })
                  )}
                </div>
              </div>
            </div>
           {user &&  <div className="absolute bottom-0 left-0 right-0 w-full p-2 bg-[#18181A] ">
              <div
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className={cn(
                  "flex items-center justify-between rounded-[8px] p-2 ",
                  isMenuOpen && "bg-[#ffffff08]"
                )}
              >
                <div
                  className={cn(
                    "flex flex-col items-start rounded-md cursor-pointer transition-all duration-300 ease-in-out"
                  )}
                >
                  <span className="text-sm font-medium">
                    {user?.user_metadata?.name}
                  </span>
                  <span className="text-xs text-[#92929A]">{user?.email}</span>
                </div>
                <MenuBar
                  isLoggingOut={isLoggingOut}
                  user={user}
                  handleGitHubConfigClick={handleGitHubConfigClick}
                  handleLogout={handleSignOut}
                  isGitHubConnected={isGitHubConnected}
                  isOpen={isMenuOpen}
                  onOpenChange={setIsMenuOpen}
                />
              </div>
            </div>}
          </motion.div>
        </>
      )}

      {/* Mobile Deployed Apps Modal */}
      <MobileDeployedAppsModal
        isOpen={isDeployedAppsModalOpen}
        onClose={() => setIsDeployedAppsModalOpen(false)}
        handleJobClick={handleJobClick}
      />
    </AnimatePresence>
  );
};

export default MobileTabSidebar;
