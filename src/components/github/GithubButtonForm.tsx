import { ChevronDown, X } from 'lucide-react';
import * as Toolt<PERSON> from "@radix-ui/react-tooltip";
import { motion } from 'motion/react';
import { cn } from '@/lib/utils';

import GithubIcon from "@/assets/github-icon.svg";       
import GithubDot from "@/assets/github/git-dot.svg"      
import ConnectedGithubActive from "@/assets/menu-icons/github_connected_hover.svg";
import GithubConnected from "@/assets/github/github_connected.svg"
import { Button } from '../ui/button';

const GitHubButtonForm = ({
  isConnected,
  isActive,
  githubUrl,
  githubTooltip,
  handleGithubSettings,
  onClearGithubUrl
}:{
    isConnected: boolean;
    isActive: boolean;
    githubUrl?: string;
    githubTooltip?: string;
    handleGithubSettings: () => void;
    onClearGithubUrl: () => void;
}) => {

  
  const getGithubIcon = () => {

    if(isConnected){
      return  GithubConnected 
    } else {
      if(isActive){
        return GithubIcon
      } else {
        return GithubIcon
      }
    }
  };

  // If we have a GitHub URL, show the repository bubble
  if (githubUrl) {
    return (
      <div className="relative flex items-center bg-[#2EE572]/5 border border-[#2EE572]/20 rounded-[30px] px-3 group">
        <Button
          className={cn(
            "pointer-events-auto flex items-center gap-2 p-0 justify-center h-[40px] bg-transparent hover:bg-transparent"
          )}
          type="button"
          onClick={handleGithubSettings}
        >
          <img
            src={getGithubIcon()}
            alt="GitHub Repository"
            className={cn("mr-1 transition-transform duration-200 transform min-w-4 min-h-4 md:min-w-5 md:min-h-5", isConnected && "scale-[1.2] mt-[2px] mr-2")}
          />
          <span className="text-sm font-medium text-[#2EE572] truncate max-w-[100px]">
            {githubUrl.split('/').pop()}
          </span>
        </Button>

        <button
          title='Clear selected repository'
          type="button"
          onClick={(e) => {
            e.stopPropagation();
            onClearGithubUrl();
          }}
          className="z-10 w-0 overflow-hidden transition-all duration-200 bg-transparent rounded-full hover:bg-transparent group-hover:w-auto group-hover:pl-1"
        >
          <X size={16} className="text-[#2ee571cd]" />
        </button>
      </div>
    );
  }

  // If no GitHub URL, show just the icon button
  return (
    <Tooltip.Root>
      <Tooltip.Trigger asChild>
        <Button
          className={cn(
            "md:px-3 md:py-2 p-2 pr-4 pointer-events-auto rounded-[30px] flex items-center justify-center h-[32px]  md:h-[40px] transition-all ease-out duration-100  gap-2",
            "bg-white bg-opacity-5 text-[#CCCCCC] hover:bg-white/10"
          )}
          type="button"
          onClick={handleGithubSettings}
        >
          <div className="relative flex items-center gap-2">
            <div className="relative flex items-center w-5 h-5 gap-2">
              <img
                src={getGithubIcon()}
                alt="GitHub"
                className={cn("w-5 h-5 transition-transform duration-200 transform", isConnected && "scale-[1.4] mt-[2px]")}
              />
              
            </div>
            <span className="hidden md:block">
              {isConnected ? "Select Repo" : "Connect GitHub"}
            </span>

            {isConnected && <ChevronDown
              className={`w-4 h-4 transition-transform duration-200 ${
                isActive ? "rotate-180" : ""
              }`}
            />}

          </div>
        </Button>
      </Tooltip.Trigger>
      {githubTooltip && (
        <Tooltip.Portal>
          <Tooltip.Content
            className="max-w-xs bg-[#fff] text-[#0f0f10] font-medium tracking-[-0.3px] text-sm px-4 py-2.5 rounded-lg shadow-lg z-[9999]"
            sideOffset={5}
            asChild
          >
            <motion.div
              initial={{ opacity: 0, y: -2, scale: 1 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 1 }}
              transition={{
                type: "tween",
                ease: "easeOut",
                duration: 0.4,
              }}
            >
              {githubTooltip}
              <Tooltip.Arrow className="fill-[#fff]" />
            </motion.div>
          </Tooltip.Content>
        </Tooltip.Portal>
      )}
    </Tooltip.Root>
  );
};

export default GitHubButtonForm;