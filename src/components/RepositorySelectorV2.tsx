import {
  useState,
  useEffect,
  useRef,
  forwardRef,
  useImperative<PERSON><PERSON>le,
} from "react";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { cn } from "@/lib/utils";
import { TabSelector, TabSelectorContent } from "./ui/tab-selector";
import { Search, X, Plus, Loader2, ChevronDown, Check } from "lucide-react";
import { Button } from "./ui/button";
import ConnectGitHubButton from "./github/ConnectGitHubButton";
import WebIcon from "./icons/Web";
import LockIcon from "@/assets/lock.svg";
import BranchIcon from "@/assets/branch.svg";
import GithubIcon from "@/assets/github-icon.svg";
import RepositoryIcon from "@/assets/repo.svg";
import { useGitHub } from "@/hooks/useGitHubAPI";
import {
  useGetGitHubInstallationsQuery,
  useGetGitHubRepositoriesQuery,
  useGetGitHubUserDetailsQuery,
  useGetGitHubBranchesQuery
} from "@/store/api/apiSlice";
import { processAndSortGitHubInstallations, type GitHubInstallation } from "@/lib/utils/gitHubUtils";
import PulseDot from "./PulseDot";

interface Repository {
  id: string;
  repository_id: string;
  name: string;
  full_name: string;
  private: boolean;
  installation_id: string;
}



interface Branch {
  name: string;
  protected: boolean;
  commit?: {
    sha: string;
    url: string;
  };
}

export interface RepositorySelectorProps {
  githubUrl: string;
  branchName: string;
  onGithubUrlChange: (url: string) => void;
  onBranchNameChange: (branch: string) => void;
  className?: string;
}

export interface RepositorySelectorRef {
  reset: () => void;
}

export const RepositorySelectorV2 = forwardRef<
  RepositorySelectorRef,
  RepositorySelectorProps
>(
  (
    { githubUrl, branchName, onGithubUrlChange, onBranchNameChange, className },
    ref
  ) => {
    const [activeTab, setActiveTab] = useState<string>("private");
    const [isTabChanging, setIsTabChanging] = useState<boolean>(false);

    // Private repository state
    const [privateGithubUrl, setPrivateGithubUrl] = useState<string>("");
    const [privateBranchName, setPrivateBranchName] = useState<string>("");

    // Public repository state
    const [publicGithubUrl, setPublicGithubUrl] = useState<string>("");
    const [publicBranchName, setPublicBranchName] = useState<string>("");


    const [selectedInstallation, setSelectedInstallation] =
      useState<string>("");
    const [repositories, setRepositories] = useState<Repository[]>([]);
    const [filteredRepositories, setFilteredRepositories] = useState<
      Repository[]
    >([]);
    const [searchQuery, setSearchQuery] = useState<string>("");
    const [branchSearchQuery, setBranchSearchQuery] = useState<string>("");
    const [publicBranchSearchQuery, setPublicBranchSearchQuery] =
      useState<string>("");
    const [isLoadingInstallations, setIsLoadingInstallations] = useState(false);
    const [isLoadingRepositories, setIsLoadingRepositories] = useState(false);
    const [branches, setBranches] = useState<Branch[]>([]);
    const [filteredBranches, setFilteredBranches] = useState<Branch[]>([]);
    const [error, setError] = useState<string | null>(null);
    const [isLoadingBranches, setIsLoadingBranches] = useState(false);
    const [isRepoDropdownOpen, setIsRepoDropdownOpen] = useState(false);
    const [isBranchDropdownOpen, setIsBranchDropdownOpen] = useState(false);

    // Public tab dropdown states
    const [isPublicRepoDropdownOpen, setIsPublicRepoDropdownOpen] =
      useState(false);
    const [isPublicBranchDropdownOpen, setIsPublicBranchDropdownOpen] =
      useState(false);
    const [publicUrlError, setPublicUrlError] = useState<string>("");

    // Installation dropdown state
    const [showInstallationDropdown, setShowInstallationDropdown] =
      useState(false);

    // Track if we're in the process of adding a new GitHub account
    const [isAddingAccount, setIsAddingAccount] = useState(false);

    // Refs for dropdowns
    const repoDropdownRef = useRef<HTMLDivElement>(null);
    const branchDropdownRef = useRef<HTMLDivElement>(null);
    const publicRepoDropdownRef = useRef<HTMLDivElement>(null);
    const publicBranchDropdownRef = useRef<HTMLDivElement>(null);

    const lastFetchedRepo = useRef<string | null>(null);
    const [selectedRepo, setSelectedRepo] = useState<string>("");
    const isFetchingBranches = useRef<boolean>(false);

    // State for tracking current repository for branch fetching
    const [currentRepoForBranches, setCurrentRepoForBranches] = useState<{
      accountLogin: string;
      repoName: string;
    } | null>(null);

    // Debug function to track currentRepoForBranches changes
    const setCurrentRepoForBranchesWithLogging = (value: { accountLogin: string; repoName: string } | null) => {
      console.log("Setting currentRepoForBranches:", { from: currentRepoForBranches, to: value });
      setCurrentRepoForBranches(value);
    };

    const [availableAccounts, setAvailableAccounts] = useState<
      GitHubInstallation[]
    >([]);
    const repoListReadyTimeout = useRef<NodeJS.Timeout | null>(null);

    // GitHub hooks
    const { redirectToGitHubInstallation, isConnected } = useGitHub();
    const {
      data: installationsData = [],
      isLoading: isLoadingInstallationsQuery,
      refetch: refetchInstallations
    } = useGetGitHubInstallationsQuery();

    // Get user details for primary account identification
    const {
      data: userDetails
    } = useGetGitHubUserDetailsQuery();

    // Get branches for the current repository using RTK Query
    const {
      data: branchesData,
      isFetching: isLoadingBranchesQuery,
      error: branchesError,
      refetch: refetchBranches
    } = useGetGitHubBranchesQuery(
      currentRepoForBranches
        ? {
            accountLogin: currentRepoForBranches.accountLogin,
            repoName: currentRepoForBranches.repoName
          }
        : { accountLogin: '', repoName: '' }, // Skip query if no repo selected
      {
        skip: !currentRepoForBranches
      }
    );



    // Get repositories using RTK Query - only when we have a valid installation
    const {
      data: allRepositories = [],
      isLoading: isLoadingRepositoriesQuery
    } = useGetGitHubRepositoriesQuery(
      selectedInstallation && selectedInstallation !== "add-new-account" ? selectedInstallation : "",
      {
        skip: !selectedInstallation || selectedInstallation === "add-new-account"
      }
    );



    // Expose reset method to parent components
    useImperativeHandle(ref, () => ({
      reset: () => {
        // Reset all state
        setPrivateGithubUrl("");
        setPrivateBranchName("");
        setPublicGithubUrl("");
        setPublicBranchName("");
        setPublicUrlError("");
        setSelectedInstallation("");
        setRepositories([]);
        setFilteredRepositories([]);
        setSearchQuery("");
        setBranchSearchQuery("");
        setPublicBranchSearchQuery("");
        setBranches([]);
        setFilteredBranches([]);

        // Reset dropdown states
        setIsRepoDropdownOpen(false);
        setIsBranchDropdownOpen(false);
        setIsPublicRepoDropdownOpen(false);
        setIsPublicBranchDropdownOpen(false);

        setError(null);
        lastFetchedRepo.current = null;
        setSelectedRepo("");

        // Clear the current repo for branches
        setCurrentRepoForBranchesWithLogging(null);
      },
    }));

    const tabs = [
      {
        id: "private",
        label: "Private Repository",
        icon: <img src={LockIcon} alt="Lock" />,
      },
      {
        id: "public",
        label: "Public Repository",
        icon: <WebIcon className="w-5 h-5" />,
      },
    ];

    // Initialize state from props on component mount
    useEffect(() => {
      if (githubUrl) {
        if (activeTab === "public") {
          setPublicGithubUrl(githubUrl);
        } else {
          setPrivateGithubUrl(githubUrl);
        }
      }

      if (branchName) {
        if (activeTab === "public") {
          setPublicBranchName(branchName);
        } else {
          setPrivateBranchName(branchName);
        }
      }
    }, []);

    // Handle tab change with loading state
    const handleTabChange = (tabId: string) => {
      setIsTabChanging(true);
      setActiveTab(tabId);

      // Update the parent component with the appropriate values based on the active tab
      if (tabId === "public") {
        onGithubUrlChange(publicGithubUrl);
        onBranchNameChange(publicBranchName);
      } else {
        onGithubUrlChange(privateGithubUrl);
        onBranchNameChange(privateBranchName);
      }

      // Simulate a short delay for the transition
      setTimeout(() => {
        setIsTabChanging(false);
      }, 100);
    };

    // Update loading state based on RTK Query
    useEffect(() => {
      setIsLoadingInstallations(isLoadingInstallationsQuery);
    }, [isLoadingInstallationsQuery]);

    // Update loading state when RTK Query loading changes
    useEffect(() => {
      setIsLoadingRepositories(isLoadingRepositoriesQuery);
    }, [isLoadingRepositoriesQuery]);

    // Update branches loading state
    useEffect(() => {
      setIsLoadingBranches(isLoadingBranchesQuery);
    }, [isLoadingBranchesQuery]);

    // Handle branches data from RTK Query
    useEffect(() => {
      console.log("Branches data updated:", { branchesData, branchesError, currentRepoForBranches });

      if (branchesData) {
        console.log("Setting branches:", branchesData);
        setBranches(branchesData);
        setFilteredBranches(branchesData);

        // Reset the fetching flag
        isFetchingBranches.current = false;

        // Auto-select default branch (usually 'main' or 'master') if available
        const defaultBranch = branchesData.find(
          (b: Branch) => b.name === "main" || b.name === "master"
        );

        if (defaultBranch) {
          handleBranchChange(defaultBranch.name);
        } else if (branchesData.length > 0) {
          handleBranchChange(branchesData[0].name);
        }
      } else if (branchesError) {
        console.error("Error fetching branches:", branchesError);
        setError("Failed to fetch branches. Please try again.");
        console.log("Clearing branches due to error");
        setBranches([]);
        setFilteredBranches([]);

        // Reset the fetching flag
        isFetchingBranches.current = false;
      } else if (branchesData === undefined && !branchesError) {
        console.log("Branches data is undefined, not clearing existing branches");
      }
    }, [branchesData, branchesError]);

    // Update repositories when RTK Query data changes - prevent infinite loops
    useEffect(() => {
      // Skip if we don't have a valid installation selected
      if (!selectedInstallation || selectedInstallation === "add-new-account") {
        return;
      }



      const newRepos = allRepositories || [];
      setRepositories(newRepos);

      // Only update filtered repositories if there's no active search
      if (searchQuery.trim() === "") {
        setFilteredRepositories(newRepos);
      }
    }, [selectedInstallation, allRepositories?.length, searchQuery]); // Stable dependencies

    // Filter repositories based on search query
    useEffect(() => {
      // Debounce the filtering operation
      const timer = setTimeout(() => {
        if (searchQuery.trim() === "") {
          // When no search query, show all repositories
          setFilteredRepositories(repositories);
        } else {
          // When there's a search query, filter repositories by name or full_name
          const query = searchQuery.toLowerCase();
          const filtered = repositories.filter(
            (repo) =>
              repo.name.toLowerCase().includes(query) ||
              repo.full_name.toLowerCase().includes(query)
          );
          setFilteredRepositories(filtered);
        }
      }, 100); // Small debounce for smoother experience

      return () => clearTimeout(timer);
    }, [repositories, searchQuery]);

    // Filter branches based on search query
    useEffect(() => {
      const currentSearchQuery =
        activeTab === "public" ? publicBranchSearchQuery : branchSearchQuery;

      if (currentSearchQuery.trim() === "") {
        setFilteredBranches(branches);
      } else {
        const query = currentSearchQuery.toLowerCase();
        const filtered = branches.filter((branch) =>
          branch.name.toLowerCase().includes(query)
        );
        setFilteredBranches(filtered);
      }
    }, [branches, branchSearchQuery, publicBranchSearchQuery, activeTab]);

    // Close dropdowns when clicking outside
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        // Use a small delay to allow for input focus/blur events to complete
        setTimeout(() => {
          // Check if the click is outside of the installation dropdown
          if (
            showInstallationDropdown &&
            !(event.target as HTMLElement).closest(".installation-dropdown") &&
            !(event.target as HTMLElement).closest(".installation-input")
          ) {
            setShowInstallationDropdown(false);
          }

          // Private tab dropdowns
          if (
            repoDropdownRef.current &&
            !repoDropdownRef.current.contains(event.target as Node) &&
            !repoDropdownRef.current.contains(document.activeElement)
          ) {
            setIsRepoDropdownOpen(false);
          }
          if (
            branchDropdownRef.current &&
            !branchDropdownRef.current.contains(event.target as Node) &&
            !branchDropdownRef.current.contains(document.activeElement)
          ) {
            setIsBranchDropdownOpen(false);
          }

          // Public tab dropdowns
          if (
            publicRepoDropdownRef.current &&
            !publicRepoDropdownRef.current.contains(event.target as Node) &&
            !publicRepoDropdownRef.current.contains(document.activeElement)
          ) {
            setIsPublicRepoDropdownOpen(false);
          }
          if (
            publicBranchDropdownRef.current &&
            !publicBranchDropdownRef.current.contains(event.target as Node) &&
            !publicBranchDropdownRef.current.contains(document.activeElement)
          ) {
            setIsPublicBranchDropdownOpen(false);
          }
        }, 50);
      };

      document.addEventListener("mousedown", handleClickOutside);
      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
        // Clear any pending timeouts on unmount
        if (repoListReadyTimeout.current) {
          clearTimeout(repoListReadyTimeout.current);
        }
      };
    }, []);



    // Trigger branch fetching for a repository using RTK Query
    const fetchBranches = (repoFullName: string, forceRefetch: boolean = false) => {
      console.log("fetchBranches called:", { repoFullName, forceRefetch, currentRepoForBranches, isFetching: isFetchingBranches.current });

      if (!repoFullName) return;

      // Prevent multiple simultaneous fetch calls
      if (isFetchingBranches.current && !forceRefetch) {
        console.log("Already fetching branches, skipping duplicate call");
        return;
      }

      isFetchingBranches.current = true;

      // Extract account_login and repo_name from full_name (format: owner/repo)
      const [account_login, repo_name] = repoFullName.split("/");

      if (!account_login || !repo_name) {
        setError("Invalid repository name format");
        isFetchingBranches.current = false;
        return;
      }

      // Check if we're selecting the same repository and need to force refetch
      const isSameRepo = currentRepoForBranches?.accountLogin === account_login &&
                        currentRepoForBranches?.repoName === repo_name;

      console.log("Repository comparison:", { isSameRepo, forceRefetch, account_login, repo_name });

      // Only clear state if it's a different repository or not a force refetch
      if (!isSameRepo && !forceRefetch) {
        console.log("Clearing branch state for new repository");
        // Clear branch-related state
        setBranches([]);
        setFilteredBranches([]);
        setBranchSearchQuery("");

        // Clear branch name when fetching branches for a new repository
        if (activeTab === "public") {
          setPublicBranchName("");
        } else {
          setPrivateBranchName("");
        }
        onBranchNameChange("");
      }

      setError(null);

      // Set the current repo for branches - this will trigger RTK Query
      setCurrentRepoForBranchesWithLogging({
        accountLogin: account_login,
        repoName: repo_name
      });

      // If we need to force refetch, call refetch after setting the repo
      if (forceRefetch || isSameRepo) {
        console.log("Calling refetch after setting currentRepoForBranches");
        // Use setTimeout to ensure the state update has been processed
        setTimeout(async () => {
          try {
            const result = await refetchBranches();
            console.log("Refetch result:", result);
            if (result.data) {
              console.log("Processing refetch data directly:", result.data);
              setBranches(result.data);
              setFilteredBranches(result.data);

              // Auto-select default branch
              const defaultBranch = result.data.find(
                (b: Branch) => b.name === "main" || b.name === "master"
              );
              if (defaultBranch) {
                handleBranchChange(defaultBranch.name);
              } else if (result.data.length > 0) {
                handleBranchChange(result.data[0].name);
              }
            }
          } catch (error) {
            console.error("Refetch error:", error);
          }
        }, 100);
      }
    };

    // Generate GitHub URL from repository name
    const getGitHubUrl = (repoFullName: string) => {
      return `https://github.com/${repoFullName}`;
    };

    // Extract repository name from GitHub URL
    const getRepoFullNameFromUrl = (url: string) => {
      if (!url) return "";
      // Updated regex to include dots, underscores, and other valid characters in repository names
      const match = url.match(/github\.com\/([\w.-]+\/[\w.-]+)/);
      if (match) {
        let repoFullName = match[1];
        // Remove .git suffix if present
        if (repoFullName.endsWith('.git')) {
          repoFullName = repoFullName.slice(0, -4);
        }
        return repoFullName;
      }
      return "";
    };

    // Validate if URL is a valid GitHub repository URL
    const isValidGitHubUrl = (url: string) => {
      if (!url) return false;
      // Check if it's a GitHub URL with proper format
      const githubUrlPattern = /^https?:\/\/github\.com\/[\w.-]+\/[\w.-]+(\.git)?$/;
      return githubUrlPattern.test(url);
    };

    // Clean and validate GitHub URL
    const cleanGitHubUrl = (url: string) => {
      if (!url) return "";

      // Remove .git suffix if present
      let cleanUrl = url.endsWith('.git') ? url.slice(0, -4) : url;

      // Ensure it starts with https://
      if (cleanUrl.startsWith('http://')) {
        cleanUrl = cleanUrl.replace('http://', 'https://');
      } else if (!cleanUrl.startsWith('https://')) {
        cleanUrl = `https://${cleanUrl}`;
      }

      return cleanUrl;
    };

    // Handle installation change
    const handleInstallationChange = (value: string) => {
      if (value === "add-new-account") {
        handleAddNewAccount();
        return;
      }

      setSelectedInstallation(value);
      setRepositories([]);
      setFilteredRepositories([]);
      setBranches([]);
      onBranchNameChange("");
      setSelectedRepo("");
    };

    // Handle repository change
    const handleRepositoryChange = (repoUrl: string) => {
      // Clear branch name when repository changes
      if (activeTab === "public") {
        setPublicGithubUrl(repoUrl);
        setPublicBranchName("");
      } else {
        setPrivateGithubUrl(repoUrl);
        setPrivateBranchName("");
      }

      // Update parent component with new repository URL and empty branch name
      onGithubUrlChange(repoUrl);
      onBranchNameChange("");

      // Reset branch-related state
      setBranches([]);
      setFilteredBranches([]);
      setBranchSearchQuery("");

      // Fetch branches for the selected repository
      const repoFullName = getRepoFullNameFromUrl(repoUrl);
      if (repoFullName) {
        // Reset the lastFetchedRepo to ensure we fetch branches for the newly selected repo
        lastFetchedRepo.current = null;
        // Add a small delay before fetching branches to ensure UI updates first
        setTimeout(() => {
          fetchBranches(repoFullName, true); // Force refetch for repository changes
        }, 100);
      }
    };

    // Handle branch change
    const handleBranchChange = (value: string) => {
      if (activeTab === "public") {
        setPublicBranchName(value);
        // Don't close dropdown here - let the click handler handle it
      } else {
        setPrivateBranchName(value);
        // Don't close dropdown here - let the click handler handle it
      }

      onBranchNameChange(value);
    };

    // Handle adding a new GitHub account
    const handleAddNewAccount = async () => {
      try {
        setIsAddingAccount(true);
        setError(null);

        // Web implementation using the centralized method from GitHubContext
        redirectToGitHubInstallation({
          isPopup: true,
          onSuccess: () => {
            refetchInstallations();
          },
        });
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "An unexpected error occurred"
        );
        console.error("Error connecting to GitHub:", err);
      } finally {
        setIsAddingAccount(false);
      }
    };

    // Clear selected repository
    const clearSelectedRepository = () => {
      if (activeTab === "public") {
        setPublicGithubUrl("");
        setPublicBranchName("");
        setPublicUrlError(""); // Clear any URL validation errors
      } else {
        setPrivateGithubUrl("");
        setPrivateBranchName("");
      }

      onGithubUrlChange("");
      onBranchNameChange("");
      lastFetchedRepo.current = null;

      // Clear the current repo for branches to ensure RTK Query refetches when same repo is selected again
      setCurrentRepoForBranchesWithLogging(null);

      // Clear branch-related state
      setBranches([]);
      setFilteredBranches([]);
      setBranchSearchQuery("");
      setPublicBranchSearchQuery("");
    };

    // Update public repository URL
    const handlePublicGithubUrlChange = (
      e: React.ChangeEvent<HTMLInputElement>
    ) => {
      const inputUrl = e.target.value;

      // Clear any previous error
      setPublicUrlError("");

      // If input is empty, allow it
      if (!inputUrl) {
        setPublicGithubUrl("");
        if (activeTab === "public") {
          onGithubUrlChange("");
        }
        return;
      }

      // Clean the URL (remove .git, ensure https)
      const cleanedUrl = cleanGitHubUrl(inputUrl);

      // Validate if it's a GitHub URL
      if (!isValidGitHubUrl(cleanedUrl)) {
        // Show error but still update the input to show what user typed
        setPublicUrlError("Please enter a valid GitHub repository URL");
        setPublicGithubUrl(inputUrl); // Keep the invalid input visible
        return;
      }

      // Valid GitHub URL - use the cleaned version
      setPublicGithubUrl(cleanedUrl);

      if (activeTab === "public") {
        onGithubUrlChange(cleanedUrl);

        // If the URL is a valid GitHub repository URL, fetch branches
        const repoFullName = getRepoFullNameFromUrl(cleanedUrl);
        if (
          repoFullName &&
          repoFullName !== getRepoFullNameFromUrl(publicGithubUrl)
        ) {
          // Reset the lastFetchedRepo to ensure we fetch branches for the newly entered repo
          lastFetchedRepo.current = null;


          // Add a small delay before fetching branches to ensure UI updates first
          setTimeout(() => {
            fetchBranches(repoFullName, true); // Force refetch for URL changes
          }, 100);
        }
      }
    };

    // Note: We're using the common handleBranchChange function for both tabs

    // Define a single loading state for the component
    const isLoading =
      isLoadingInstallations ||
      isLoadingRepositories ||
      isLoadingBranches ||
      isTabChanging;

    // Ensure branch name is preserved when component is remounted
    useEffect(() => {
      // Only proceed if we have a GitHub URL (meaning a repository is selected)
      const currentGithubUrl =
        activeTab === "public" ? publicGithubUrl : privateGithubUrl;
      const currentBranchName =
        activeTab === "public" ? publicBranchName : privateBranchName;

      if (currentGithubUrl) {
        if (currentBranchName) {
          // If we already have a branch name from state, keep it
        } else if (branches.length > 0) {
          // If we have branches but no branch name, select a default
          const defaultBranch = branches.find(
            (b) => b.name === "main" || b.name === "master"
          );
          if (defaultBranch) {
            handleBranchChange(defaultBranch.name);
          } else {
            handleBranchChange(branches[0].name);
          }
        }

        // If we have a repository selected but no branches loaded yet, fetch them
        // Only fetch if we're not already loading branches and haven't attempted to fetch yet
        // Also don't fetch if currentRepoForBranches is null (repository was just cleared)
        // Also don't fetch if we're already in the process of fetching branches
        if (branches.length === 0 && !isLoadingBranches && currentRepoForBranches && !isFetchingBranches.current) {
          const repoFullName = getRepoFullNameFromUrl(currentGithubUrl);
          if (repoFullName) {
            // Store the repo name we're fetching branches for to prevent infinite loops
            if (
              !lastFetchedRepo.current ||
              lastFetchedRepo.current !== repoFullName
            ) {
              lastFetchedRepo.current = repoFullName;
              fetchBranches(repoFullName);
            }
          }
        }
      }
    }, [
      activeTab,
      branches,
      isLoadingBranches,
      publicGithubUrl,
      publicBranchName,
      privateGithubUrl,
      privateBranchName,
      currentRepoForBranches,
    ]);

    // Process installations data when it changes - with guards to prevent infinite loops
    useEffect(() => {
      // Skip if we don't have installations data, user details, or already have accounts
      if (!installationsData || installationsData.length === 0 || !userDetails?.github?.account_name || availableAccounts.length > 0) {
        return;
      }

      const processInstallationsData = () => {
        try {
          const primaryAccountName = userDetails.github.account_name;

          // Use the utility function to process and sort installations
          const sortedAccounts = processAndSortGitHubInstallations(
            installationsData,
            primaryAccountName
          );

          // Select primary account by default if available, otherwise first account
          const defaultAccount =
            sortedAccounts.find(
              (account: GitHubInstallation) => account.isPrimary
            )?.installation_id || sortedAccounts[0]?.installation_id;

          setSelectedInstallation(defaultAccount);
          setAvailableAccounts(sortedAccounts);
        } catch (err) {
          console.error("Error processing GitHub installations:", err);
          setError("Failed to process GitHub installations. Please try again.");
        }
      };

      processInstallationsData();
    }, [installationsData?.length, userDetails?.github?.account_name, availableAccounts.length]); // Depend on user details and installations

    return (
      <div
        className={cn(
          "space-y-4 border border-[#ffffff12] rounded-xl",
          className
        )}
      >
        <TabSelector
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={handleTabChange}
          className="w-full p-2 pb-0"
        />

        {isTabChanging && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 text-[#4ADE80] animate-spin" />
          </div>
        )}

        {!isTabChanging && (
          <>
            <TabSelectorContent
              tabId="public"
              activeTab={activeTab}
              className="px-6 py-0 pt-2 pb-6"
            >

                <div className="flex flex-col gap-6">
                  {/* Repository Selector */}
                  <div className="flex items-center gap-2">
                    <div className="flex flex-col flex-1 gap-2">
                      <div className="flex items-center justify-between gap-1">
                        <div className="flex items-center gap-1">
                          <WebIcon className="w-5 h-5 text-[#898F99]" />
                          <Label
                            htmlFor="public-repo-selector"
                            className="text-[#898F99] font-[Inter] text-sm font-medium"
                          >
                            Repository URL
                          </Label>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <div
                          className="relative flex-1"
                          ref={publicRepoDropdownRef}
                        >
                          <div
                            className={cn(
                              "flex items-center justify-between w-full h-14 bg-[#131314] border rounded-[8px] p-3 text-white repository-input",
                              isTabChanging && "opacity-70 cursor-not-allowed",
                              isPublicRepoDropdownOpen && "border-white/50",
                              publicUrlError ? "border-[#FF4545]" : "border-[#242424]"
                            )}
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              if (!isTabChanging) {
                                setIsPublicRepoDropdownOpen(true);
                              }
                            }}
                          >
                            <div className="flex items-center w-full">
                              <Search className="h-4 w-4 text-[#8F8F98] mr-2" />
                              <Input
                                type="text"
                                value={publicGithubUrl}
                                onChange={handlePublicGithubUrlChange}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (!isTabChanging) {
                                    setIsPublicRepoDropdownOpen(true);
                                  }
                                }}
                                onFocus={(e) => {
                                  e.stopPropagation();
                                  if (!isTabChanging) {
                                    setIsPublicRepoDropdownOpen(true);
                                  }
                                }}
                                onBlur={(e) => {
                                  // Prevent dropdown from closing immediately on blur
                                  // Only close if the click is outside the dropdown
                                  const relatedTarget = e.relatedTarget as Node;
                                  if (
                                    !publicRepoDropdownRef.current?.contains(
                                      relatedTarget
                                    )
                                  ) {
                                    // Add a small delay to allow for clicks on dropdown items
                                    setTimeout(() => {
                                      if (
                                        !publicRepoDropdownRef.current?.contains(
                                          document.activeElement
                                        )
                                      ) {
                                        setIsPublicRepoDropdownOpen(false);
                                      }
                                    }, 150);
                                  }
                                }}
                                placeholder="Enter a GitHub repository URL"
                                className="w-full p-0 bg-transparent border-0 focus-visible:ring-0 focus-visible:ring-offset-0 text-[#C4C4CC] font-medium font-['Inter']"
                                disabled={isTabChanging}
                              />
                            </div>
                            <div
                              className="flex items-center justify-center w-8 h-8 ml-2 cursor-pointer"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                if (!isTabChanging) {
                                  setIsPublicRepoDropdownOpen(
                                    !isPublicRepoDropdownOpen
                                  );
                                }
                              }}
                            >
                              {isTabChanging ? (
                                <Loader2 className="h-5 w-5 animate-spin text-[#8F8F98]" />
                              ) : (
                                <ChevronDown
                                  className={cn(
                                    "h-5 w-5 transition-transform duration-200",
                                    isPublicRepoDropdownOpen
                                      ? "transform rotate-180"
                                      : "",
                                    "text-[#8F8F98]"
                                  )}
                                />
                              )}
                            </div>
                          </div>

                          {/* Public repository dropdown */}
                          {isPublicRepoDropdownOpen && (
                            <div className="absolute z-50 w-full mt-1 bg-[#131314] border border-[#242424] rounded-md shadow-lg max-h-[300px] overflow-y-auto">
                              <div className="p-2">
                                <div className="px-3 py-2 text-[#8F8F98] text-sm">
                                  Enter a GitHub repository URL (e.g., https://github.com/username/repo)
                                  <br />
                                  <span className="text-[#6B7280] text-xs">
                                    Only GitHub URLs are supported. .git suffix will be automatically removed.
                                  </span>
                                </div>
                                {publicGithubUrl && (
                                  <div
                                    className="px-3 min-h-[40px] rounded-[8px] py-2 text-[#DDDDE6] hover:bg-[#2D2D2F]/40 font-medium cursor-pointer flex items-center justify-between"
                                    onClick={() => {
                                      // Use the current URL
                                      handleRepositoryChange(publicGithubUrl);

                                      setIsPublicRepoDropdownOpen(false);
                                    }}
                                  >
                                    <div className="flex items-center gap-2">
                                      <span>
                                        {getRepoFullNameFromUrl(
                                          publicGithubUrl
                                        )}
                                      </span>
                                    </div>
                                    <Check className="h-4 w-4 text-[#1BB4CC]" />
                                  </div>
                                )}
                              </div>
                            </div>
                          )}

                          {publicGithubUrl && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                clearSelectedRepository();

                                // Ensure dropdowns are closed
                                setIsPublicRepoDropdownOpen(false);
                                setIsPublicBranchDropdownOpen(false);
                              }}
                              className="absolute w-8 h-8 transform -translate-y-1/2 rounded-full right-10 top-1/2 hover:bg-transparent focus:outline-none"
                              title="Clear selected repository"
                            >
                              <X className="h-4 w-4 text-[#8F8F98] mx-auto" />
                            </Button>
                          )}
                        </div>
                      </div>

                      {/* URL validation error */}
                      {publicUrlError && (
                        <div className="text-[#FF4545] text-sm font-medium font-['Inter'] mt-1">
                          {publicUrlError}
                        </div>
                      )}
                    </div>

                    {/* Branch Selector */}
                    <div className="flex flex-col items-start gap-2">
                      <div className="flex items-center gap-1">
                        <img
                          alt="Branch"
                          src={BranchIcon}
                          className="w-5 h-5"
                        />
                        <Label
                          htmlFor="public-branch-name"
                          className="text-[#898F99] font-[Inter] text-sm font-medium"
                        >
                          Branch
                        </Label>
                      </div>
                      <div
                        className="relative w-48"
                        ref={publicBranchDropdownRef}
                      >
                        <div
                          className={cn(
                            "flex items-center justify-between w-full h-14 bg-[#131314] border border-[#242424] rounded-[8px] p-3 text-white repository-input",
                            (isTabChanging || !publicGithubUrl) &&
                              "opacity-70 cursor-not-allowed",
                            isPublicBranchDropdownOpen && "border-white/50"
                          )}
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            if (!isTabChanging && publicGithubUrl) {
                              setIsPublicBranchDropdownOpen(true);
                            }
                          }}
                        >
                          <div className="relative flex items-center w-full">
                            {/* <Search className="h-4 w-4 text-[#8F8F98] mr-2" /> */}
                            <Input
                              type="text"
                              value={
                                isPublicBranchDropdownOpen
                                  ? publicBranchSearchQuery
                                  : publicBranchName
                              }
                              onClick={(e) => {
                                e.stopPropagation();
                                if (!isTabChanging && publicGithubUrl) {
                                  setIsPublicBranchDropdownOpen(true);
                                  // Clear search query when opening dropdown to allow fresh search
                                  setPublicBranchSearchQuery("");
                                }
                              }}
                              onFocus={(e) => {
                                e.stopPropagation();
                                if (!isTabChanging && publicGithubUrl) {
                                  setIsPublicBranchDropdownOpen(true);
                                  // Clear search query when opening dropdown to allow fresh search
                                  setPublicBranchSearchQuery("");
                                }
                              }}
                              onBlur={(e) => {
                                // Prevent dropdown from closing immediately on blur
                                // Only close if the click is outside the dropdown
                                const relatedTarget = e.relatedTarget as Node;
                                if (
                                  !publicBranchDropdownRef.current?.contains(
                                    relatedTarget
                                  )
                                ) {
                                  // Add a small delay to allow for clicks on dropdown items
                                  setTimeout(() => {
                                    if (
                                      !publicBranchDropdownRef.current?.contains(
                                        document.activeElement
                                      )
                                    ) {
                                      setIsPublicBranchDropdownOpen(false);
                                      // Clear search query when closing dropdown
                                      setPublicBranchSearchQuery("");
                                    }
                                  }, 150);
                                }
                              }}
                              onChange={(e) => {
                                e.stopPropagation();
                                if (isPublicBranchDropdownOpen) {
                                  // When dropdown is open, update search query only
                                  setPublicBranchSearchQuery(e.target.value);
                                }
                                // Don't allow direct editing when dropdown is closed
                              }}
                              placeholder={
                                isLoadingBranches
                                  ? "Loading branches..."
                                  : branches.length === 0 && publicGithubUrl
                                  ? "No branches found"
                                  : !publicGithubUrl
                                  ? "Select a repository first"
                                  : "Enter or select branch name"
                              }
                              className="w-full p-0 bg-transparent border-0 focus-visible:ring-0 focus-visible:ring-offset-0 text-[#C4C4CC] font-medium font-['Inter']"
                              disabled={isTabChanging || !publicGithubUrl}
                            />

                            {/* Clear branch button */}
                            {/* {publicBranchName && !isLoadingBranches && publicGithubUrl && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                // Clear branch name
                                handleBranchChange("");
                                setPublicBranchSearchQuery("");
                              }}
                              className="absolute right-0 w-6 h-6 rounded-full hover:bg-transparent focus:outline-none"
                              title="Clear branch name"
                            >
                              <X className="h-3 w-3 text-[#8F8F98] mx-auto" />
                            </Button>
                          )} */}
                          </div>
                          <div
                            className="flex items-center justify-center w-8 h-8 ml-2 cursor-pointer"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              if (!isTabChanging && publicGithubUrl) {
                                setIsPublicBranchDropdownOpen(
                                  !isPublicBranchDropdownOpen
                                );
                              }
                            }}
                          >
                            {isTabChanging ? (
                              <Loader2 className="h-5 w-5 animate-spin text-[#8F8F98]" />
                            ) : (
                              <ChevronDown
                                className={cn(
                                  "h-5 w-5 transition-transform duration-200",
                                  isPublicBranchDropdownOpen
                                    ? "transform rotate-180"
                                    : "",
                                  !publicGithubUrl
                                    ? "text-[#8F8F98]/50"
                                    : "text-[#8F8F98]",
                                  isTabChanging || !publicGithubUrl
                                    ? "opacity-50 cursor-not-allowed"
                                    : ""
                                )}
                              />
                            )}
                          </div>
                        </div>

                        {/* Public branch dropdown */}
                        {isPublicBranchDropdownOpen && publicGithubUrl && (
                          <div className="absolute z-50 w-full mt-1 bg-[#131314] border border-[#242424] rounded-md shadow-lg max-h-[300px] overflow-y-auto">
                            <div className="max-h-[300px] overflow-y-auto py-1">
                              {isLoadingBranches ? null : branches.length === 0 ? (
                                <div className="px-3 py-2 text-[#8F8F98] text-sm">
                                  No branches available for this repository
                                </div>
                              ) : (
                                <>
                                  {/* Filtered branches */}
                                  {filteredBranches.length === 0 ? (
                                    <div className="px-3 py-2 text-[#8F8F98] text-sm">
                                      {publicBranchSearchQuery
                                        ? "No matching branches found"
                                        : "No branches found"}
                                    </div>
                                  ) : (
                                    <div className="flex flex-col gap-[6px] p-2">
                                      {/* <div className="px-3 py-2 text-[#8F8F98] text-sm">
                                        {publicBranchSearchQuery
                                          ? "Matching branches:"
                                          : "Select a branch:"}
                                      </div> */}
                                      {filteredBranches.map((branch) => (
                                        <div
                                          key={branch.name}
                                          className={cn(
                                            "px-3 min-h-[40px] rounded-[8px] py-2 text-[#DDDDE6] hover:bg-[#2D2D2F]/40 font-medium cursor-pointer",
                                            branch.name === publicBranchName &&
                                              "bg-[#172426] text-[#1BB4CC] hover:bg-[#172426] hover:text-[#1BB4CC]"
                                          )}
                                          onClick={(e) => {
                                            e.preventDefault();
                                            e.stopPropagation();
                                            // Clear search query
                                            setPublicBranchSearchQuery("");
                                            // Close dropdown immediately to provide visual feedback
                                            setIsPublicBranchDropdownOpen(
                                              false
                                            );
                                            // Set branch name with a small delay to ensure UI updates first
                                            setTimeout(() => {
                                              handleBranchChange(branch.name);
                                            }, 50);
                                          }}
                                        >
                                          <div className="flex items-center justify-between w-full">
                                            <span>{branch.name}</span>
                                            {branch.name ===
                                              publicBranchName && (
                                              <Check className="h-4 w-4 text-[#1BB4CC]" />
                                            )}
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  )}
                                </>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
           
            </TabSelectorContent>

            <TabSelectorContent
              tabId="private"
              activeTab={activeTab}
              className="px-6 py-0 pt-2 pb-6"
            >
              {isLoadingInstallations ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 text-[#4ADE80] animate-spin" />
                </div>
              ) : !isConnected ? (
                <div className="relative p-6 overflow-hidden rounded-lg">
                  {/* Angled lines background */}
                  <div className="absolute inset-0 overflow-hidden">
                    <div
                      className="absolute inset-0"
                      style={{
                        backgroundImage: `repeating-linear-gradient(
                        45deg,
                        transparent,
                        transparent 4px,
                        rgba(255, 255, 255, 0.05) 4px,
                        rgba(255, 255, 255, 0.05) 6px,
                        transparent 6px,
                        transparent 10px
                      )`,
                      }}
                    ></div>
                  </div>

                  {/* Content */}
                  <div className="relative z-10 flex items-start justify-between">
                    <div className="flex flex-col items-start space-y-2">
                      <h2 className="text-base font-['Inter'] font-bold text-white">
                        Github Authentication Required
                      </h2>
                      <p className="text-[#5C5F66] text-base font-['Inter'] max-w-md">
                        Connect your GitHub account to access all your private
                        and public repositories.
                      </p>
                    </div>
                    <div className="flex items-center justify-center">
                      <ConnectGitHubButton className="rounded-full bg-[#009939] hover:bg-[#********] transition-all ease-in-out opacity-100" />
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col gap-6">
                  {/* GitHub Account/Installation Selector */}
                  <div className="flex flex-col gap-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-1">
                        <img
                          alt="GitHub"
                          src={GithubIcon}
                          className="w-4 h-4"
                        />
                        <Label
                          htmlFor="installation-selector"
                          className="text-[#898F99] font-[Inter] text-sm font-medium"
                        >
                          Connected Organizations
                        </Label>
                      </div>

                      <div className="flex items-center gap-1 font-[Inter] text-sm font-medium text-[#2EE572]">
                        <PulseDot
                          color="#2EE572"
                          size={20}
                          innerSize={8}
                          animate={true}
                          className=""
                        />{" "}
                        {availableAccounts.find((account) => account.isPrimary)
                          ?.installation_id || "Primary Account"}
                      </div>
                    </div>
                    <div className="relative">
                      <div
                        className={`flex items-center justify-between w-full h-14 bg-[#131314] border ${
                          showInstallationDropdown
                            ? "border-white/50"
                            : "border-[#242424]"
                        } rounded-md p-3 text-white installation-input`}
                        onClick={() => {
                          // Close other dropdowns
                          setIsRepoDropdownOpen(false);
                          setIsBranchDropdownOpen(false);
                          // Toggle installation dropdown
                          setShowInstallationDropdown(
                            !showInstallationDropdown
                          );
                        }}
                      >
                        <div className="flex items-center w-full">
                          <div className="relative flex-1">
                            {selectedInstallation ? (
                              <div className="flex items-center">
                                {availableAccounts.find(
                                  (i) =>
                                    i.installation_id === selectedInstallation
                                )?.account_login || "Select a GitHub account"}
                                {availableAccounts.find(
                                  (i) =>
                                    i.installation_id === selectedInstallation
                                )?.account_type === "User" &&
                                  !availableAccounts.find(
                                    (i) =>
                                      i.installation_id === selectedInstallation
                                  )?.isPrimary && (
                                    <span className="text-xs text-[#DDDDE6] rounded-full bg-[#FFFFFF0D] ml-1 px-2 py-1 font-['Inter'] font-medium">
                                      Collaborator
                                    </span>
                                  )}
                              </div>
                            ) : (
                              <span className="text-[#8F8F98]">
                                Select a GitHub account
                              </span>
                            )}
                          </div>
                        </div>
                        <ChevronDown
                          className={`h-4 w-4 text-[#898F99] transition-transform ${
                            showInstallationDropdown ? "rotate-180" : ""
                          }`}
                        />
                      </div>

                      {/* Installation dropdown */}
                      {availableAccounts.length > 0 &&
                        showInstallationDropdown && (
                          <div className="absolute z-50 w-full mt-1 bg-[#131314] border border-[#242424] rounded-md shadow-lg max-h-[300px] overflow-y-auto installation-dropdown">
                            <div className="flex flex-col gap-[6px] p-2">
                              {availableAccounts.map((installation) => (
                                <div
                                  key={installation.installation_id}
                                  className={cn(
                                    "px-3 min-h-[40px] rounded-[8px] py-2  text-[#DDDDE6] hover:bg-[#2D2D2F]/40 font-medium cursor-pointer",
                                    selectedInstallation ===
                                      installation.installation_id &&
                                      "bg-[#172426] text-[#1BB4CC] hover:bg-[#172426] hover:text-[#1BB4CC]"
                                  )}
                                  onClick={() => {
                                    handleInstallationChange(
                                      installation.installation_id
                                    );
                                    setShowInstallationDropdown(false);
                                  }}
                                >
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                      <span>{installation.account_login}</span>
                                      {installation.account_type === "User" &&
                                        !installation.isPrimary && (
                                          <span className="text-xs text-[#DDDDE6] rounded-full bg-[#FFFFFF0D] ml-1 px-2 py-1 font-['Inter'] font-medium">
                                            Collaborator
                                          </span>
                                        )}
                                    </div>
                                    {selectedInstallation ===
                                      installation.installation_id && (
                                      <Check className="h-4 w-4 text-[#1BB4CC]" />
                                    )}
                                  </div>
                                </div>
                              ))}
                              <div className="border-t border-[#ffffff12] my-1"></div>
                              <div
                                className="px-3 min-h-[40px] rounded-[8px] py-2 text-[#4ADE80] hover:bg-[#172426] hover:text-[#4ADE80] font-medium cursor-pointer"
                                onClick={() => {
                                  handleAddNewAccount();
                                  setShowInstallationDropdown(false);
                                }}
                              >
                                <div className="flex items-center gap-2">
                                  {isAddingAccount ? (
                                    <Loader2 className="w-4 h-4 animate-spin" />
                                  ) : (
                                    <Plus className="w-4 h-4" />
                                  )}
                                  {isAddingAccount
                                    ? "Adding Account..."
                                    : "Add New Github Organizations"}
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                    </div>
                  </div>

                  {/* Repository Selector */}
                  <div className="flex items-center gap-2">
                    <div className="flex flex-col flex-1 gap-2">
                      <div className="flex items-center justify-between gap-1">
                        <div className="flex items-center gap-1">
                          <img
                            alt="Repository"
                            src={RepositoryIcon}
                            className="w-5 h-5"
                          />
                          <Label
                            htmlFor="repo-selector"
                            className="text-[#898F99] font-[Inter] text-sm font-medium"
                          >
                            Select Repo
                          </Label>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <div className="relative flex-1" ref={repoDropdownRef}>
                          <div
                            className={cn(
                              "flex items-center justify-between w-full h-14 bg-[#131314] border border-[#242424] rounded-[8px] p-3 text-white repository-input",
                              (!selectedInstallation ||
                                (isLoadingRepositories &&
                                  repositories.length === 0)) &&
                                "opacity-70 cursor-not-allowed",
                              isRepoDropdownOpen && "border-white/50"
                            )}
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              if (
                                selectedInstallation &&
                                !isLoadingRepositories &&
                                repositories.length > 0
                              ) {
                                // Close installation dropdown
                                setShowInstallationDropdown(false);
                                // Open repository dropdown
                                setIsRepoDropdownOpen(true);
                              }
                            }}
                          >
                            <div className="flex items-center w-full">
                              <Search className="h-4 w-4 text-[#8F8F98] mr-2" />
                              <Input
                                type="text"
                                value={searchQuery}
                                onChange={(e) => {
                                  e.stopPropagation();
                                  setSearchQuery(e.target.value);
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (
                                    selectedInstallation &&
                                    !isLoadingRepositories &&
                                    repositories.length > 0
                                  ) {
                                    // Close installation dropdown
                                    setShowInstallationDropdown(false);
                                    // Open repository dropdown
                                    setIsRepoDropdownOpen(true);
                                  }
                                }}
                                onFocus={(e) => {
                                  e.stopPropagation();
                                  if (
                                    selectedInstallation &&
                                    !isLoadingRepositories &&
                                    repositories.length > 0
                                  ) {
                                    // Close installation dropdown
                                    setShowInstallationDropdown(false);
                                    // Open repository dropdown
                                    setIsRepoDropdownOpen(true);
                                  }
                                }}
                                onBlur={(e) => {
                                  // Prevent dropdown from closing immediately on blur
                                  // Only close if the click is outside the dropdown
                                  const relatedTarget = e.relatedTarget as Node;
                                  if (
                                    !repoDropdownRef.current?.contains(
                                      relatedTarget
                                    )
                                  ) {
                                    // Add a small delay to allow for clicks on dropdown items
                                    setTimeout(() => {
                                      if (
                                        !repoDropdownRef.current?.contains(
                                          document.activeElement
                                        )
                                      ) {
                                        setIsRepoDropdownOpen(false);
                                      }
                                    }, 150);
                                  }
                                }}
                                placeholder={
                                  isLoadingRepositories
                                    ? "Loading repositories..."
                                    : repositories.length === 0
                                    ? "No repositories found"
                                    : selectedRepo
                                    ? `Selected: ${selectedRepo}`
                                    : "Search for a repository"
                                }
                                className="w-full p-0  bg-transparent border-0 focus-visible:ring-0 focus-visible:ring-offset-0 text-[#C4C4CC] font-medium font-['Inter']"
                                disabled={
                                  !selectedInstallation ||
                                  (isLoadingRepositories &&
                                    repositories.length === 0)
                                }
                              />
                            </div>
                            <div
                              className="flex items-center justify-center w-8 h-8 cursor-pointer"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                if (
                                  selectedInstallation &&
                                  !isLoadingRepositories &&
                                  repositories.length > 0
                                ) {
                                  // Close installation dropdown
                                  setShowInstallationDropdown(false);
                                  // Toggle repository dropdown
                                  setIsRepoDropdownOpen(!isRepoDropdownOpen);
                                }
                              }}
                            >
                              {isLoadingRepositories ? (
                                <Loader2 className="h-5 w-5 animate-spin text-[#8F8F98]" />
                              ) : (
                                <ChevronDown
                                  className={cn(
                                    "h-5 w-5 transition-transform duration-200",
                                    isRepoDropdownOpen
                                      ? "transform rotate-180"
                                      : "",
                                    !selectedInstallation ||
                                      repositories.length === 0
                                      ? "text-[#8F8F98]/50"
                                      : "text-[#8F8F98]",
                                    !selectedInstallation ||
                                      isLoadingRepositories ||
                                      repositories.length === 0
                                      ? "opacity-50 cursor-not-allowed"
                                      : ""
                                  )}
                                />
                              )}
                            </div>
                          </div>

                          {privateGithubUrl && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                clearSelectedRepository();
                                setSelectedRepo("");
                                setSearchQuery("");
                                // Ensure dropdowns are closed
                                setIsRepoDropdownOpen(false);
                                setIsBranchDropdownOpen(false);
                              }}
                              className="absolute w-8 h-8 transform -translate-y-1/2 rounded-full right-10 top-1/2 hover:bg-transparent focus:outline-none"
                              title="Clear selected repository"
                            >
                              <X className="h-4 w-4 text-[#8F8F98] mx-auto" />
                            </Button>
                          )}

                          {isRepoDropdownOpen && selectedInstallation && (
                            <div className="absolute mt-1 w-full bg-[#131314] border border-[#242424] rounded-md shadow-lg z-10">
                              <div className="max-h-[300px] overflow-y-auto py-1">
                                {isLoadingRepositories ? (
                                  <div className="flex items-center justify-center p-4">
                                    <Loader2 className="h-5 w-5 text-[#4ADE80] animate-spin mr-2" />
                                    <span className="text-white">
                                      Loading repositories...
                                    </span>
                                  </div>
                                ) : filteredRepositories.length === 0 ? (
                                  <div className="px-3 py-2 text-[#8F8F98] text-sm">
                                    {searchQuery
                                      ? "No repositories match your search"
                                      : "No repositories found"}
                                  </div>
                                ) : (
                                  <div className="flex flex-col gap-[6px] p-2">
                                    {filteredRepositories.map((repo) => (
                                      <div
                                        key={repo.id}
                                        className={cn(
                                          "px-3 min-h-[40px] rounded-[8px] py-2 text-[#DDDDE6] hover:bg-[#2D2D2F]/40 font-medium cursor-pointer",
                                          selectedRepo === repo.name &&
                                            "bg-[#172426] text-[#1BB4CC] hover:bg-[#172426] hover:text-[#1BB4CC]"
                                        )}
                                        onClick={(e) => {
                                          e.preventDefault();
                                          e.stopPropagation();
                                          // Set state values first
                                          setSelectedRepo(repo.name);
                                          setSearchQuery(repo.name);
                                          // Clear branch name in UI
                                          setBranchSearchQuery("");
                                          // Close dropdown immediately to provide visual feedback
                                          setIsRepoDropdownOpen(false);
                                          // Then handle repository change with a small delay to ensure UI updates first
                                          setTimeout(() => {
                                            handleRepositoryChange(
                                              getGitHubUrl(repo.full_name)
                                            );
                                          }, 50);
                                        }}
                                      >
                                        <div className="flex items-center justify-between w-full">
                                          <span>{repo.name}</span>
                                          {selectedRepo === repo.name && (
                                            <Check className="h-4 w-4 text-[#1BB4CC]" />
                                          )}
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Branch Selector */}
                    <div className="flex flex-col items-start gap-2">
                      <div className="flex items-center gap-1">
                        <img
                          alt="Branch"
                          src={BranchIcon}
                          className="w-5 h-5"
                        />
                        <Label
                          htmlFor="branch-name"
                          className="text-[#898F99] font-[Inter] text-sm font-medium"
                        >
                          Select Branch
                        </Label>
                      </div>
                      <div className="relative w-48" ref={branchDropdownRef}>
                        <div
                          className={cn(
                            "flex items-center justify-between w-full h-14 bg-[#131314] border border-[#242424] rounded-[8px] p-3 text-white repository-input",
                            (!privateGithubUrl || isLoadingBranches) &&
                              "opacity-70 cursor-not-allowed",
                            isBranchDropdownOpen && "border-white/50"
                          )}
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            if (privateGithubUrl && !isLoadingBranches) {
                              // Close other dropdowns
                              setShowInstallationDropdown(false);
                              setIsRepoDropdownOpen(false);
                              // Open branch dropdown
                              setIsBranchDropdownOpen(true);
                            }
                          }}
                        >
                          <div className="relative flex items-center w-full">
                            {/* <Search className="h-4 w-4 text-[#8F8F98] mr-2" /> */}
                            <Input
                              type="text"
                              value={
                                isBranchDropdownOpen
                                  ? branchSearchQuery
                                  : privateBranchName
                              }
                              onClick={(e) => {
                                e.stopPropagation();
                                if (privateGithubUrl && !isLoadingBranches) {
                                  // Close other dropdowns
                                  setShowInstallationDropdown(false);
                                  setIsRepoDropdownOpen(false);
                                  // Open branch dropdown
                                  setIsBranchDropdownOpen(true);
                                  // Clear search query when opening dropdown to allow fresh search
                                  setBranchSearchQuery("");
                                }
                              }}
                              onFocus={(e) => {
                                e.stopPropagation();
                                if (privateGithubUrl && !isLoadingBranches) {
                                  // Close other dropdowns
                                  setShowInstallationDropdown(false);
                                  setIsRepoDropdownOpen(false);
                                  // Open branch dropdown
                                  setIsBranchDropdownOpen(true);
                                  // Clear search query when opening dropdown to allow fresh search
                                  setBranchSearchQuery("");
                                }
                              }}
                              onBlur={(e) => {
                                // Prevent dropdown from closing immediately on blur
                                // Only close if the click is outside the dropdown
                                const relatedTarget = e.relatedTarget as Node;
                                if (
                                  !branchDropdownRef.current?.contains(
                                    relatedTarget
                                  )
                                ) {
                                  // Add a small delay to allow for clicks on dropdown items
                                  setTimeout(() => {
                                    if (
                                      !branchDropdownRef.current?.contains(
                                        document.activeElement
                                      )
                                    ) {
                                      setIsBranchDropdownOpen(false);
                                      // Clear search query when closing dropdown
                                      setBranchSearchQuery("");
                                    }
                                  }, 150);
                                }
                              }}
                              onChange={(e) => {
                                e.stopPropagation();
                                if (isBranchDropdownOpen) {
                                  // When dropdown is open, update search query only
                                  setBranchSearchQuery(e.target.value);
                                }
                                // Don't allow direct editing when dropdown is closed
                              }}
                              placeholder={
                                isLoadingBranches
                                  ? "Loading branches..."
                                  : branches.length === 0 && privateGithubUrl
                                  ? "No branches found"
                                  : !privateGithubUrl
                                  ? "Select a repository first"
                                  : "Enter or select branch name"
                              }
                              className="w-full p-0 bg-transparent border-0 focus-visible:ring-0 focus-visible:ring-offset-0 text-[#C4C4CC] font-medium font-['Inter']"
                              disabled={!privateGithubUrl || isLoadingBranches}
                            />
                          </div>
                          <div
                            className="flex items-center justify-center w-8 h-8 ml-2 cursor-pointer"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              if (privateGithubUrl && !isLoadingBranches) {
                                // Close other dropdowns
                                setShowInstallationDropdown(false);
                                setIsRepoDropdownOpen(false);
                                // Toggle branch dropdown
                                setIsBranchDropdownOpen(!isBranchDropdownOpen);
                              }
                            }}
                          >
                            {isLoadingBranches ? (
                              <Loader2 className="h-5 w-5 animate-spin text-[#8F8F98]" />
                            ) : (
                              <ChevronDown
                                className={cn(
                                  "h-5 w-5 transition-transform duration-200",
                                  isBranchDropdownOpen
                                    ? "transform rotate-180"
                                    : "",
                                  !privateGithubUrl
                                    ? "text-[#8F8F98]/50"
                                    : "text-[#8F8F98]",
                                  !privateGithubUrl || isLoadingBranches
                                    ? "opacity-50 cursor-not-allowed"
                                    : ""
                                )}
                              />
                            )}
                          </div>
                        </div>

                        {isBranchDropdownOpen && privateGithubUrl && (
                          <div className="absolute mt-1 w-full bg-[#131314] border border-[#242424] rounded-[8px] shadow-lg z-10">
                            <div className="max-h-[300px] overflow-y-auto py-1">
                              {isLoadingBranches ? null : branches.length === 0 ? (
                                <div className="px-3 py-2 text-[#8F8F98] text-sm">
                                  No branches available for this repository
                                </div>
                              ) : (
                                <>
                                  {/* Filtered branches */}
                                  {filteredBranches.length === 0 ? (
                                    <div className="px-3 py-2 text-[#8F8F98] text-sm">
                                      {branchSearchQuery
                                        ? "No matching branches found"
                                        : "No branches found"}
                                    </div>
                                  ) : (
                                    <div className="flex flex-col gap-[6px] p-2">
                                      {/* <div className="px-3 py-2 text-[#8F8F98] text-sm">
                                        {branchSearchQuery
                                          ? "Matching branches:"
                                          : "Select a branch:"}
                                      </div> */}
                                      {filteredBranches.map((branch) => (
                                        <div
                                          key={branch.name}
                                          className={cn(
                                            "px-3 min-h-[40px] rounded-[8px] py-2 text-[#DDDDE6] hover:bg-[#2D2D2F]/40 font-medium cursor-pointer",
                                            branch.name === privateBranchName &&
                                              "bg-[#172426] text-[#1BB4CC] hover:bg-[#172426] hover:text-[#1BB4CC]"
                                          )}
                                          onClick={(e) => {
                                            e.preventDefault();
                                            e.stopPropagation();
                                            // Clear search query
                                            setBranchSearchQuery("");
                                            // Close dropdown immediately to provide visual feedback
                                            setIsBranchDropdownOpen(false);
                                            // Set branch name with a small delay to ensure UI updates first
                                            setTimeout(() => {
                                              handleBranchChange(branch.name);
                                            }, 50);
                                          }}
                                        >
                                          <div className="flex items-center justify-between w-full">
                                            <span>{branch.name}</span>
                                            {branch.name ===
                                              privateBranchName && (
                                              <Check className="h-4 w-4 text-[#1BB4CC]" />
                                            )}
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  )}
                                </>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Status Messages */}
                  {error && (
                    <p className="text-[#FF4545] font-[Inter] text-sm font-medium">
                      {error}
                    </p>
                  )}
                  {!isLoading &&
                    !error &&
                    availableAccounts.length > 0 &&
                    selectedInstallation &&
                    selectedInstallation !== "add-new-account" &&
                    repositories.length === 0 && (
                      <p className="text-[#DDDDE6]/50 font-[Inter] text-sm font-medium">
                        No repositories found for this account
                      </p>
                    )}
                  {!isLoading &&
                    !error &&
                    searchQuery &&
                    filteredRepositories.length === 0 &&
                    repositories.length > 0 && (
                      <p className="text-[#DDDDE6]/50 font-[Inter] text-sm font-medium">
                        No repositories match your search
                      </p>
                    )}
                  {!isLoading && !isLoadingBranches &&
                    !error &&
                    privateGithubUrl &&
                    !isLoadingBranches &&
                    branches.length === 0 && (
                      <p className="text-[#DDDDE6]/50 font-[Inter] text-sm font-medium">
                        No branches found for this repository. Please check if
                        the repository is empty or has any branches.
                      </p>
                    )}
                </div>
              )}
            </TabSelectorContent>
          </>
        )}
      </div>
    );
  }
);

export default RepositorySelectorV2;
