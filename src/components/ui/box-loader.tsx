import { useEffect, useRef, useState } from 'react';

interface BoxLoaderProps {
  progress?: number; // 0 to 1
  boxWidth?: number; // width in pixels (deprecated - now responsive)
  boxHeight?: number; // height in pixels
  gapWidth?: number; // gap in pixels
  className?: string;
  minBoxes?: number; // minimum number of boxes to show (deprecated - now responsive)
  maxBoxes?: number; // maximum number of boxes to show (deprecated - now responsive)
  debug?: boolean; // enable debug mode
}

export function BoxLoader({
  progress = 0.55, // 11/20 as default
  boxWidth, // deprecated - now responsive
  boxHeight = 40, // h-10 equivalent (40px)
  gapWidth = 8, // gap-2 equivalent
  className = '',
  minBoxes, // deprecated - now responsive
  maxBoxes, // deprecated - now responsive
  debug = false, // debug mode off by default
}: BoxLoaderProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [boxes, setBoxes] = useState<number>(10); // Start with mobile default
  const [currentProgress, setCurrentProgress] = useState<number>(Math.max(0.05, progress)); // Initialize with at least 5%
  const [containerWidth, setContainerWidth] = useState<number>(0);
  const [isMobile, setIsMobile] = useState<boolean>(true);

  // Use refs to track the actual current progress value and animation frame
  const progressRef = useRef<number>(Math.max(0.05, progress));
  const animationFrameRef = useRef<number | null>(null);

  // Log initial progress value
  useEffect(() => {
    //console.log(`BoxLoader: Initialized with progress ${Math.max(0.05, progress) * 100}%`);
  }, []);

  // Smoothly animate progress changes
  useEffect(() => {
    // Allow progress to reset to 0 or very small values (component restart)
    // but prevent other backward progress
    const isReset = progress <= 0.1 && progressRef.current > 0.1;

    if (!isReset && progress <= progressRef.current) {
      if (debug) {
        //console.log(`BoxLoader: Skipping animation - new progress ${progress * 100}% <= current ${progressRef.current * 100}%`);
      }
      return;
    }

    // Handle reset case - immediately set to new progress without animation
    if (isReset) {
      progressRef.current = Math.max(0.05, progress);
      setCurrentProgress(Math.max(0.05, progress));
      if (debug) {
        console.log(`BoxLoader: Reset progress to ${(Math.max(0.05, progress) * 100).toFixed(1)}%`);
      }
      return;
    }

    // Start from current progress and animate to target progress
    const startProgress = progressRef.current;
    const endProgress = progress;
    const duration = 800; // Longer animation duration for smoother effect
    const startTime = performance.now();

    //console.log(`BoxLoader: Animating progress from ${startProgress * 100}% to ${endProgress * 100}%`);

    // Cancel any existing animation frame
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }

    const animateProgress = (timestamp: number) => {
      const elapsed = timestamp - startTime;
      const progressRatio = Math.min(elapsed / duration, 1);
      // Use easeOutQuad for smoother animation
      const easeProgress = 1 - (1 - progressRatio) * (1 - progressRatio);
      const newProgress = startProgress + (endProgress - startProgress) * easeProgress;

      // Update the ref value first
      progressRef.current = newProgress;

      // Only log significant changes to avoid console spam
      if (Math.abs(newProgress - currentProgress) > 0.01) {
        //console.log(`BoxLoader: Progress update ${(newProgress * 100).toFixed(1)}%`);
      }

      // Then update the state
      setCurrentProgress(newProgress);

      if (progressRatio < 1) {
        animationFrameRef.current = requestAnimationFrame(animateProgress);
      } else {
        //console.log(`BoxLoader: Animation complete at ${(newProgress * 100).toFixed(1)}%`);
        animationFrameRef.current = null;
      }
    };

    animationFrameRef.current = requestAnimationFrame(animateProgress);

    // Cleanup function
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
    };
  }, [progress, currentProgress, debug]);

  // Calculate boxes based on container width and screen size
  useEffect(() => {
    const calculateBoxes = () => {
      if (!containerRef.current) return;
      const width = containerRef.current.offsetWidth;
      setContainerWidth(width);

      // Determine if mobile based on screen width
      const isMobileScreen = window.innerWidth < 768; // md breakpoint
      setIsMobile(isMobileScreen);

      // Set responsive values
      const responsiveBoxWidth = isMobileScreen ? 24 : 30;
      const responsiveBoxCount = isMobileScreen ? 10 : 14;

      // Use responsive box count directly instead of calculating from width
      setBoxes(responsiveBoxCount);

      if (debug) {
        console.log(`BoxLoader: Container width: ${width}px, isMobile: ${isMobileScreen}, boxWidth: ${responsiveBoxWidth}, boxes: ${responsiveBoxCount}`);
      }
    };

    // Calculate immediately
    calculateBoxes();

    // Use ResizeObserver for more reliable size detection
    const resizeObserver = new ResizeObserver(() => {
      calculateBoxes();
    });

    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    // Fallback to window resize event
    window.addEventListener('resize', calculateBoxes);

    // Calculate again after a short delay to ensure container is fully rendered
    const timeoutId = setTimeout(calculateBoxes, 100);

    return () => {
      resizeObserver.disconnect();
      window.removeEventListener('resize', calculateBoxes);
      clearTimeout(timeoutId);
    };
  }, [debug]);

  const filledBoxes = Math.round(boxes * currentProgress);

  if (debug) {
    //console.log(`BoxLoader: Progress: ${progress}, Current: ${currentProgress}, Filled: ${filledBoxes}/${boxes}`);
  }

  return (
    <div ref={containerRef} className={`md:w-full py-4 md:py-0 ${className}`}>
      <div className="flex gap-2">
        {[...Array(boxes)].map((_, index) => (
          <div
            key={index}
            className="bg-[#142C33] p-[2px] rounded-[2px] h-[40px] w-[24px] md:w-[30px]"
          >
            <div
              className={`h-full rounded-[2px] flex items-center justify-center transition-colors duration-300 ease-out w-[20px] md:w-[26px]`}
              style={{
                backgroundColor: index < filledBoxes ? 'var(--loader-color, #1BB4CC)' : 'transparent',
              }}
            />
          </div>
        ))}
      </div>
    </div>
  );
}
