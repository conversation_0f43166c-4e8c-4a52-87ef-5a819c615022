import { useState, useEffect, useRef, useCallback, ReactNode } from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useArrowNavigation } from "@/hooks/useArrowNavigation";
import WhiteArrow from "@/assets/showcase/white_arrow.svg"

export interface SliderCard {
  id: string | number;
  content: ReactNode;
}

interface SliderProps {
  cards: SliderCard[];
  onCardClick?: (card: SliderCard, index: number) => void;
  autoAdvance?: boolean;
  autoAdvanceDuration?: number;
  className?: string;
  cardClassName?: string;
  showNavigation?: boolean;
  showProgressDots?: boolean;
  pauseOnHover?: boolean;
  wrapAround?: boolean;
  centerCardWidth?: number;
  centerCardHeight?: number;
  sideCardWidth?: number;
  sideCardHeight?: number;
  sideCardOpacity?: number;
  sideCardScale?: number;
  spacing?: number;
  animationDuration?: number;
}

export const Slider = ({
  cards,
  onCardClick,
  autoAdvance = true,
  autoAdvanceDuration = 2500,
  className,
  cardClassName,
  showNavigation = true,
  showProgressDots = true,
  pauseOnHover = true,
  wrapAround = true,
  centerCardWidth = 350,
  centerCardHeight = 200,
  sideCardWidth = 280,
  sideCardHeight = 170,
  sideCardOpacity = 0.6,
  sideCardScale = 0.9,
  spacing = 350,
  animationDuration = 500,
}: SliderProps) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [progressKey, setProgressKey] = useState(0);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const nextSlide = useCallback(() => {
    if (isTransitioning || cards.length <= 1) return;
    setIsTransitioning(true);
    setCurrentIndex((prev) => wrapAround ? (prev + 1) % cards.length : Math.min(prev + 1, cards.length - 1));
    setTimeout(() => setIsTransitioning(false), animationDuration);
  }, [isTransitioning, cards.length, wrapAround, animationDuration]);

  const prevSlide = useCallback(() => {
    if (isTransitioning || cards.length <= 1) return;
    setIsTransitioning(true);
    setCurrentIndex((prev) => wrapAround ? (prev - 1 + cards.length) % cards.length : Math.max(prev - 1, 0));
    setTimeout(() => setIsTransitioning(false), animationDuration);
  }, [isTransitioning, cards.length, wrapAround, animationDuration]);

  // Reset progress key when slide changes
  useEffect(() => {
    setProgressKey(prev => prev + 1);
  }, [currentIndex]);

  // Simple auto-advance functionality
  useEffect(() => {
    if (!autoAdvance || cards.length <= 1) return;
    if (isHovered && pauseOnHover) return;

    intervalRef.current = setInterval(() => {
      nextSlide();
    }, autoAdvanceDuration);

    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, [nextSlide, autoAdvance, autoAdvanceDuration, isHovered, pauseOnHover, cards.length]);

  // Arrow navigation
  useArrowNavigation({
    onNext: nextSlide,
    onPrevious: prevSlide,
  });

  // Get item style based on position
  const getItemStyle = (index: number) => {
    const diff = index - currentIndex;
    const totalItems = cards.length;

    let position = diff;
    if (wrapAround) {
      if (diff > totalItems / 2) position -= totalItems;
      if (diff < -totalItems / 2) position += totalItems;
    }

    const baseTranslate = position * spacing;
    const scale = position === 0 ? 1 : sideCardScale;
    const opacity = position === 0 ? 1 : Math.abs(position) === 1 ? sideCardOpacity : 0;
    const zIndex = position === 0 ? 20 : Math.abs(position) === 1 ? 10 : 0;

    const width = position === 0 ? centerCardWidth : sideCardWidth;
    const height = position === 0 ? centerCardHeight : sideCardHeight;

    return {
      transform: `translateX(${baseTranslate}px) scale(${scale})`,
      opacity: opacity,
      zIndex: zIndex,
      width: `${width}px`,
      height: `${height}px`,
      transition: `all ${animationDuration / 1000}s ease-out`,
    };
  };

  const handleManualNavigation = (index: number) => {
    if (!isTransitioning && index !== currentIndex) {
      setIsTransitioning(true);
      setCurrentIndex(index);
      setTimeout(() => setIsTransitioning(false), animationDuration);
    }
  };

  const handleCardClick = (card: SliderCard, index: number) => {
    if (index !== currentIndex) {
      handleManualNavigation(index);
    } else if (onCardClick) {
      onCardClick(card, index);
    }
  };

  if (cards.length === 0) return null;

  return (
    <>
      <style>{`
        @keyframes fillProgress {
          from {
            width: 0%;
          }
          to {
            width: 100%;
          }
        }

        .progress-fill {
          animation: fillProgress linear forwards;
        }

        .progress-fill.paused {
          animation-play-state: paused;
        }
      `}</style>

      <div className={cn("flex flex-col items-center justify-center w-full overflow-hidden", className)}>
        {/* Cards Container */}
        <div className="relative flex items-center justify-center w-full md:h-[500px] h-screen md:mb-8">
          <div className="relative flex items-center justify-center w-full h-full">
            {cards.map((card, index) => {
              const style = getItemStyle(index);
              const diff = index - currentIndex;
              const totalItems = cards.length;
              let position = diff;
              if (wrapAround) {
                if (diff > totalItems / 2) position -= totalItems;
                if (diff < -totalItems / 2) position += totalItems;
              }
              const isCenter = position === 0;

              return (
                <div
                  key={card.id}
                  className={cn(
                    "absolute select-none rounded-[10px] flex items-center justify-center cursor-pointer",
                    cardClassName
                  )}
                  style={style}
                  onMouseEnter={() => pauseOnHover && setIsHovered(true)}
                  onMouseLeave={() => pauseOnHover && setIsHovered(false)}
                  onClick={() => handleCardClick(card, index)}
                >
                  {card.content}
                  
                  {/* Overlay for non-center items */}
                  {!isCenter && (
                    <div className="absolute inset-0 transition-colors duration-200 rounded-[10px]" />
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Navigation and Progress Dots */}
        {(showNavigation || showProgressDots) && (
          <div className="flex flex-col items-center justify-center gap-6 mt-4">
            <div className="flex items-center justify-center space-x-4">
              {/* Previous Button */}
              {showNavigation && (
                <button
                  type="button"
                  onClick={prevSlide}
                  className={cn(
                    "h-8 w-8  backdrop-blur-lg text-white-bg bg-white rounded-full transition-all duration-200 flex items-center justify-center",
                    isTransitioning
                      ? "opacity-50 cursor-not-allowed"
                      : "opacity-80 hover:opacity-100"
                  )}
                  aria-label="Previous"
                >
                  <img src={WhiteArrow} alt="Next" className="w-4 h-4" />
                </button>
              )}

              {/* Progress Dots */}
              {showProgressDots && (
                <div className="flex space-x-2">
                  {cards.map((_, index) => (
                    <motion.button
                      key={index}
                      type="button"
                      onClick={() => handleManualNavigation(index)}
                      className={cn(
                        "relative h-2 rounded-full overflow-hidden transition-all duration-300",
                        index === currentIndex
                          ? "w-12 bg-[#FFFFFF]/20"
                          : "w-2 bg-[#FFFFFF]/20 hover:bg-[#FFFFFF]/30"
                      )}
                      aria-label={`Go to slide ${index + 1}`}
                    >
                      {/* Progress fill animation */}
                      {index === currentIndex && autoAdvance && (
                        <div
                          key={`progress-${progressKey}`}
                          className={cn(
                            "absolute inset-y-0 left-0 bg-white rounded-full progress-fill",
                            isHovered && "paused"
                          )}
                          style={{
                            animationDuration: `${autoAdvanceDuration}ms`,
                          }}
                        />
                      )}
                      {/* Static fill when no auto-advance */}
                      {index === currentIndex && !autoAdvance && (
                        <div className="absolute inset-0 bg-white rounded-full" />
                      )}
                    </motion.button>
                  ))}
                </div>
              )}

              {/* Next Button */}
              {showNavigation && (
                <button
                  type="button"
                  onClick={nextSlide}
                  className={cn(
                    "h-8 w-8 text-white-bg shadow-2xl backdrop-blur-lg bg-white rounded-full transition-all duration-200 flex items-center justify-center",
                    isTransitioning
                      ? "opacity-50 cursor-not-allowed"
                      : "opacity-80 hover:opacity-100"
                  )}
                  aria-label="Next"
                >
                  <img src={WhiteArrow} alt="Next" className="w-4 h-4 rotate-180" />
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    </>
  );
};