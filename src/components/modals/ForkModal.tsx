import { useEffect, useState, use<PERSON>emo, useCallback, useRef } from "react";
import { X } from "lucide-react";
import ForkIcon from "@/assets/fork/fork.svg";
import DollarCyanSVG from "@/assets/fork/dollar_cyan.svg";
import Clock<PERSON>yanSVG from "@/assets/fork/pace_cyan.svg";
import LikeCyanSVG from "@/assets/fork/like_cyan.svg";
import { cn } from "@/lib/utils";
import Summarize from "@/assets/fork/summary.svg";
import { TailChase } from "ldrs/react";
import "ldrs/react/TailChase.css";
import { motion, AnimatePresence } from "framer-motion";

import Summarize<PERSON>yan from "@/assets/fork/summary_cyan.svg";

// @ts-ignore
import MatrixCode from "@/assets/fork/matrix_code.gif";

import EditReview from "@/assets/fork/edit_note.svg";
import StyledMarkdown from "../StyledMarkdown";
import { agent<PERSON><PERSON> } from "@/services/agent<PERSON><PERSON>";
import { toast } from "@/hooks/use-toast";
import { DotPattern } from "../layouts/DotBackground";

interface ForkModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onFork?: (jobId: string, summaryData: string) => void;
  jobId?: string;
  messages?: any[];
  appName?: string;
  summaryData?: string;
  startInSummaryMode?: boolean;
  podIsPaused?: boolean;
}

interface InfoInterface {
  title: string;
  description: string;
  icon: string;
}

export const INFO: InfoInterface[] = [
  {
    title: "Context preserved",
    description: "All important context preserved in summary",
    icon: LikeCyanSVG,
  },

  {
    title: "Memory refreshed",
    description: "Fresh context window with clean slate for new tasks",
    icon: ClockCyanSVG,
  },
  {
    title: "Spend less",
    description: "Refreshed memory means lower token costs",
    icon: DollarCyanSVG,
  },
];

// Type definitions for parsed summary data
interface SummarySection {
  tagName: string;
  content: string;
}

// Alternative function that returns sections as an array
const parseSummaryDataAsArray = (summaryText: string): SummarySection[] => {
  if (!summaryText) {
    return [];
  }
  const sections: SummarySection[] = [];
  const tagRegex = /<(\w+)>([\s\S]*?)<\/\1>/g;
  let match;

  while ((match = tagRegex.exec(summaryText)) !== null) {
    const [, tagName, content] = match;
    sections.push({
      tagName,
      // Only trim leading/trailing newlines that are part of XML structure
      // but preserve other whitespace like spaces at end of lines
      content: content.replace(/^\n+|\n+$/g, ""),
    });
  }

  return sections;
};

const RotatingStatusText = ({ count }: { count: number }) => {
  const STATUS_MESSAGES = [
    `Analyzing ${count} Messages`,
    "Extracting feature requirements",
    "Processing code changes",
    "Summarizing development phases",
    "Reviewing testing protocols",
  ];
  const [currentIndex, setCurrentIndex] = useState(0);
  const [direction, setDirection] = useState<"forward" | "backward">("forward");

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => {
        if (direction === "forward") {
          if (prevIndex === STATUS_MESSAGES.length - 1) {
            setDirection("backward");
            return prevIndex - 1;
          }
          return prevIndex + 1;
        } else {
          if (prevIndex === 0) {
            setDirection("forward");
            return prevIndex + 1;
          }
          return prevIndex - 1;
        }
      });
    }, 2000); // Change every 2 seconds

    return () => clearInterval(interval);
  }, [direction]);

  return (
    <div className="absolute flex items-center justify-center w-full h-6 top-[5rem]">
      <AnimatePresence mode="wait">
        <motion.span
          key={currentIndex}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{
            duration: 0.5,
            ease: "easeInOut",
          }}
          className="text-[#80FFF980] text-[15px] text-nowrap font-jetbrains font-medium absolute"
        >
          {STATUS_MESSAGES[currentIndex]}
        </motion.span>
      </AnimatePresence>
    </div>
  );
};

const iconBasedOnTag = {
  analysis: "🔍",
  product_requirements: "🎯",
  key_technical_concepts: "🔑",
  code_architecture: "🏗️",
  pending_tasks: "📌",
  current_work: "📈",
  optional_next_step: "🚀",
} as const;

type IconTagName = keyof typeof iconBasedOnTag;

export const ForkModal = ({
  isOpen,
  onOpenChange,
  onFork,
  jobId,
  messages = [],
  summaryData,
  appName,
  podIsPaused,
  startInSummaryMode = false,
}: ForkModalProps) => {
  const [forkState, setForkState] = useState<
    "fork" | "summarizing" | "review" | "summary"
  >(startInSummaryMode ? "summary" : "fork");
  const [isEditingMode, setIsEditingMode] = useState(false);
  const [editedSummaryData, setEditedSummaryData] = useState(summaryData);
  const [originalSummaryData, setOriginalSummaryData] = useState(summaryData);
  const [focusedSectionIndex, setFocusedSectionIndex] = useState<number | null>(
    null
  );
  // Store the parsed sections for editing
  const [editingSections, setEditingSections] = useState<SummarySection[]>([]);

  // Handle prop changes after mount
  useEffect(() => {
    if (startInSummaryMode && summaryData) {
      console.log("Setting state to summary mode");
      setForkState("summary");
      setEditedSummaryData(summaryData);
      setOriginalSummaryData(summaryData);
    }
  }, [startInSummaryMode, summaryData]);

  const [submitingFinal, setSubmitingFinal] = useState(false);
  const closingTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Monitor podIsPaused and start 4-second timer when it becomes false after submission
  useEffect(() => {
    if (submitingFinal && podIsPaused === false && !closingTimerRef.current) {
      closingTimerRef.current = setTimeout(() => {
        onOpenChange(false);
        setSubmitingFinal(false);
        setForkState("fork");
        closingTimerRef.current = null;
      }, 4000);
    }
  }, [submitingFinal, podIsPaused]);

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (closingTimerRef.current) {
        clearTimeout(closingTimerRef.current);
        closingTimerRef.current = null;
      }
    };
  }, []);

  const handleFork = async () => {
    if (!jobId) {
      console.error("No job ID provided");
      return;
    }

    setForkState("summarizing");

    try {
      // Call the generate summary API
      const summaryResponse = await agentApi.generateSummary(jobId);

      console.log("Summary API Response:", summaryResponse);

      // Update the edited summary data with the API response
      if (summaryResponse && summaryResponse.summary) {
        setEditedSummaryData(summaryResponse.summary);
        setOriginalSummaryData(summaryResponse.summary);
      }

      // Move to review state
      setForkState("review");
    } catch (error) {
      console.error("Error generating summary:", error);
      // Handle error - maybe show an error state or go back to fork state
      setForkState("fork");
    }
  };

  const SUMMARY_DATA_ARRAY = useMemo(() => {
    if (isEditingMode && editingSections.length > 0) {
      return editingSections;
    }
    return parseSummaryDataAsArray(editedSummaryData || "");
  }, [editedSummaryData, isEditingMode, editingSections]);

  // Check if all sections are empty
  const hasAnyContent = useMemo(() => {
    return SUMMARY_DATA_ARRAY.some(
      (section) => section.content && section.content.trim().length > 0
    );
  }, [SUMMARY_DATA_ARRAY]);

  const handleContentChange = useCallback(
    (index: number, newContent: string) => {
      // Update the editing sections directly without reconstructing XML
      setEditingSections((prev) => {
        const updated = [...prev];
        updated[index] = { ...updated[index], content: newContent };
        return updated;
      });
    },
    []
  );

  const finalizeFork = async () => {
    if (!jobId) {
      console.error("No job ID provided");
      return;
    }

    setSubmitingFinal(true);

    try {
      if (!editedSummaryData) {
        console.error("No summary data provided");
        toast({
          title: "Error",
          description: "No summary data provided",
          variant: "destructive",
        });
        return;
      }

      onFork?.(jobId, editedSummaryData);
    } catch (error) {
      console.error("Error submitting fork:", error);
      // Handle error - maybe show an error state or go back to review state
      setForkState("review");
    }
  };

  if (!isOpen) return null;

  return (
    <div className="absolute w-full max-h-[calc(100vh-56px)] min-h-[calc(100vh-56px)] z-[49] flex items-center justify-center bg-[#0e0e0f50] backdrop-blur-[5px]">
      <div
        className={cn(
          " p-0 max-md:max-w-[95vw] bg-[#18181A] border border-[#242424] rounded-[16px] font-['Inter'] overflow-hidden",
          forkState === "summarizing" ? "border-[#80FFF91A]" : "",
          forkState === "summarizing"
            ? "bg-gradient-to-b from-[#131314] via-[#13131400] to-[#131314]"
            : ""
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {forkState == "fork" && (
          <>
            <div className="flex max-md:min-w-[80vw] md:max-w-[740px] flex-col gap-4 p-4 md:gap-6 md:p-8">
              <div className="flex justify-between md:gap-[10px] flex-col">
                <div className="flex items-center justify-between md:gap-[10px]">
                  <h2 className="text-[18px] md:text-[22px] font-medium text-white">
                    Fork this session with a summary
                  </h2>
                  <button
                    type="button"
                    onClick={() => onOpenChange(false)}
                    className="w-6 h-6 flex items-center justify-center text-[#737780] hover:text-white transition-colors"
                    aria-label="Close modal"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
                <p className="text-[13px] md:text-[16px] font-[400] md:leading-[24px] text-[#8A8F98] font-['Inter']">
                  Continue your work in a new conversation while keeping the
                  important context from this session, perfect for continuing
                  complex projects without losing progress.
                </p>
              </div>

              <div className="grid gap-2 md:gap-4 md:grid-cols-3">
                {/* Context preserved */}

                {INFO.map((item, index) => (
                  <div
                    key={index}
                    className="flex md:flex-col rounded-[8px] p-4 py-6 items-start w-full text-center bg-[#80FFF90A] gap-2 md:gap-[3rem]"
                  >
                    <img src={item.icon} alt={item.title} className="w-5 h-5" />
                    <div className="flex flex-col items-start w-full gap-[6px]">
                      <h3 className="text-[14px] md:text-[16px] text-[#80FFF9] font-medium">
                        {item.title}
                      </h3>
                      <p className="text-[12px] md:text-[14px] font-[500] text-start text-[#80FFF966] font-['Inter']">
                        {item.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

        {forkState == "summarizing" && (
          <>
            <div className="relative flex flex-col items-center justify-center flex-1 z-[2] max-md:min-h-[70vh] max-md:min-w-[95vw] md:min-w-[700px] md:min-h-[700px] gap-[4rem] bg-[#80fff908]">
              <div className="absolute inset-0 grid w-full h-full grid-cols-3 pointer-events-none z-[1] opacity-[0.2] overflow-hidden">
                <img
                  src={MatrixCode}
                  alt="Matrix Code"
                  className="object-cover w-full h-full"
                />
                <img
                  src={MatrixCode}
                  alt="Matrix Code"
                  className="object-cover w-full h-full"
                />
                <img
                  src={MatrixCode}
                  alt="Matrix Code"
                  className="object-cover w-full h-full"
                />
                {/* // Reverse From Here */}
                <img
                  src={MatrixCode}
                  alt="Matrix Code"
                  className="object-cover w-full h-full rotate-180"
                />
                <img
                  src={MatrixCode}
                  alt="Matrix Code"
                  className="object-cover w-full h-full rotate-180"
                />
                <img
                  src={MatrixCode}
                  alt="Matrix Code"
                  className="object-cover w-full h-full rotate-180"
                />
              </div>
              <div className="relative z-[10] ">
                {/* Main content */}
                <div className="relative radial-bg flex items-center space-x-3 bg-[#80FFF9]/10 backdrop-blur-sm p-2 px-4 rounded-lg ">
                  {/* Loading dots animation */}
                  <p className="text-[#80FFF9] text-[16px] font-medium">
                    {"Generating Summary"}
                  </p>
                  <TailChase size="20" speed="1.75" color="#80FFF9" />
                </div>
                <RotatingStatusText count={messages.length} />
              </div>

              <div
                onClick={() => {
                  setForkState("fork");
                  onOpenChange(false);
                }}
                className=" absolute  font-medium cursor-pointer bottom-[3rem] rounded-[28px] radial-bg-white backdrop-blur-xl z-[49] items-center px-4 py-[10px] flex gap-2"
              >
                <X className="w-4 h-4 md:w-5 md:h-5" />
                <span className="max-md:text-[14px]">Cancel Generation</span>
              </div>
            </div>
          </>
        )}

        {forkState == "review" && (
          <>
            <div className="flex flex-col max-md:max-h-[70vh] max-w-[inherit] z-[10]">
              <div className="flex justify-between md:gap-[6px] flex-col md:p-6 md:pb-4 p-4 bg-[#18181A]">
                <div className="flex items-center justify-between md:gap-[6px]">
                  <h2 className="text-[18px] md:text-[22px] font-medium text-white">
                    Review Summary
                  </h2>
                  {!submitingFinal && (
                    <button
                      type="button"
                      onClick={() => onOpenChange(false)}
                      className="w-6 h-6 flex items-center justify-center text-[#737780] hover:text-white transition-colors"
                      aria-label="Close modal"
                    >
                      <X className="w-5 h-5" />
                    </button>
                  )}
                </div>
                <p className="text-[13px] md:text-[14px] font-[400] md:leading-[24px] text-[#8A8F98] font-['Inter']">
                  Review the generated summary and make any edits before we fork
                  the session.
                </p>
              </div>

              <div className="flex flex-col h-full max-md:w-[calc(95vw)] md:max-w-[800px] md:min-w-[800px] relative overflow-hidden">
                <div
                  className={cn(
                    "sticky top-0 z-[10] flex justify-between p-4  backdrop-blur-lg overflow-clip",
                    isEditingMode ? "radial-bg-white" : "radial-bg"
                  )}
                >
                  {/* Context preserved */}
                  <div className="flex justify-between w-[inherit] max-h-[32px] ">
                    <div className="flex items-center gap-2">
                      <img
                        src={!isEditingMode ? SummarizeCyan : Summarize}
                        alt="Summarize"
                        className={cn("object-cover object-center w-5 h-5")}
                      />
                      <span
                        className={cn(
                          "text-[#80FFF9] font-medium  max-md:hidden md:block  leading-[20px] text-[15px] tracking-[-0.2px]",
                          isEditingMode && "text-[#FFFFFF]"
                        )}
                      >
                        {isEditingMode ? "Edit Summary" : "Session Summary"}
                      </span>
                      <span
                        className={cn(
                          "text-[#80FFF9] font-medium md:hidden leading-[20px] text-[15px] tracking-[-0.2px]",
                          isEditingMode && "text-[#FFFFFF]"
                        )}
                      >
                        {isEditingMode ? "Edit" : "Summary"}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 max-h-[32px]">
                    {isEditingMode ? (
                      <>
                        <button
                          type="button"
                          onClick={() => {
                            // Reset to original data and exit editing mode
                            setEditedSummaryData(originalSummaryData || "");
                            setEditingSections([]);
                            setIsEditingMode(false);
                          }}
                          className="flex items-center max-h-[32px] bg-[#ffffff16] gap-2 py-[6px] px-[12px] rounded-[6px] hover:bg-[#ffffff20] transition-colors"
                        >
                          <span className="text-[#FFFFFF] text-[15px] font-medium text-nowrap">
                            Cancel
                          </span>
                        </button>
                        <button
                          type="button"
                          onClick={() => {
                            // Reconstruct XML from editing sections and save
                            const updatedSummaryData = editingSections
                              .map(
                                (section) =>
                                  `<${section.tagName}>\n${section.content}\n</${section.tagName}>`
                              )
                              .join("\n");

                            setEditedSummaryData(updatedSummaryData);
                            setEditingSections([]);
                            setIsEditingMode(false);
                          }}
                          className="flex items-center max-h-[32px] bg-[#2EE572] gap-2 py-[6px] px-[12px] rounded-[6px] hover:bg-[#2EE572]/90 transition-colors"
                        >
                          <span className="text-[#0F0F10] text-[15px] leading-[20px] tracking-[-0.2px] font-semibold text-nowrap">
                            Save Changes
                          </span>
                        </button>
                      </>
                    ) : (
                      <button
                        type="button"
                        disabled={submitingFinal}
                        onClick={() => {
                          // Store current data as original before entering edit mode
                          setOriginalSummaryData(editedSummaryData);
                          // Parse current data into editing sections
                          setEditingSections(
                            parseSummaryDataAsArray(editedSummaryData || "")
                          );
                          setIsEditingMode(true);
                        }}
                        className="flex items-center min-w-[150px] bg-[#ffffff16] gap-2 py-[6px] px-[8px] rounded-[6px] hover:bg-[#ffffff20] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <img src={EditReview} alt="Edit" className="w-5 h-5" />
                        <span className="text-[#FFFFFF] text-[15px] leading-[20px] font-medium text-nowrap">
                          Edit Summary
                        </span>
                      </button>
                    )}
                  </div>
                </div>

                <div className="flex z-[10] flex-col gap-5 p-3 md:p-6 max-h-[60vh] overflow-y-scroll overflow-hidden w-full min-w-0">
                  <span
                    className={cn(
                      "text-[#FFFFFF] text-[22px] font-semibold capitalize",
                      isEditingMode && "text-[#FFFFFF50]"
                    )}
                  >
                    {appName} - Project Analysis
                  </span>

                  <div className="flex flex-col max-md:max-w-[calc(100vw - 10rem)] min-w-0 gap-4">
                    {SUMMARY_DATA_ARRAY.map((item, index) => (
                      <div
                        key={index}
                        className={cn(
                          "flex flex-col gap-2 bg-[#17181A] border rounded-[10px] p-1 transition-all duration-200 w-full min-w-0",
                          focusedSectionIndex === index
                            ? "border-[#80FFF950] shadow-[0_0_0_1px_#80FFF9] shadow-[#80FFF9]/20 bg-[#131314]"
                            : "border-[#242424]",
                          isEditingMode &&
                            focusedSectionIndex != index &&
                            "border-[#333333] bg-[#131314]"
                        )}
                      >
                        <div
                          className={cn(
                            "flex items-center justify-between p-3 px-4 gap-2 bg-[#FFFFFF0F] backdrop-blur-lg rounded-[8px]",
                            isEditingMode && "opacity-50",
                            focusedSectionIndex == index && "opacity-100"
                          )}
                        >
                          <span className="text-[#FFFFFF] text-[16px] tracking-[-0.2px] font-medium capitalize">
                            <span className="mr-2">
                              {" "}
                              {iconBasedOnTag[item.tagName as IconTagName] ||
                                "📄"}
                            </span>
                            {item.tagName.replaceAll("_", " ")}
                          </span>
                          {isEditingMode && (
                            <span className="text-[14px] font-['Inter'] font-medium">
                              {item.content.length} characters
                            </span>
                          )}
                        </div>

                        <div className="p-3 pt-0 max-md:max-w-[calc(100vw - 10rem)] overflow-hidden ">
                          {isEditingMode ? (
                            <textarea
                              value={item.content}
                              onChange={(e) =>
                                handleContentChange(index, e.target.value)
                              }
                              onFocus={() => setFocusedSectionIndex(index)}
                              onBlur={() => setFocusedSectionIndex(null)}
                              onKeyDown={(e) => {
                                e.stopPropagation();
                              }}
                              className={cn(
                                "w-full h-auto leading-[1.8] font-['Inter'] bg-transparent border-0 rounded-[6px] placeholder:text-white/80 font-medium resize-none focus:outline-none transition-colors whitespace-pre-wrap text-[14px]",
                                focusedSectionIndex == index
                                  ? "text-white"
                                  : "text-white/80"
                              )}
                              placeholder="Enter content for this section..."
                              spellCheck={false}
                              rows={Math.max(
                                5,
                                Math.ceil(item.content.length / 80)
                              )}
                              style={{
                                whiteSpace: "pre-wrap",
                                wordWrap: "break-word",
                                overflowWrap: "break-word",
                                lineHeight: "2",
                              }}
                            />
                          ) : (
                            <StyledMarkdown
                              children={
                                item.content ||
                                "No content available for this section."
                              }
                              variant="compact"
                              className="break-words text-white/80 overflow-wrap-anywhere max-w-none"
                              customComponents={{
                                p: ({ node, ...props }) => (
                                  <p
                                    className="font-medium break-words font-['Inter'] leading-[28px]"
                                    {...props}
                                  />
                                ),
                                pre: ({ ...props }) => (
                                  <pre
                                    className="p-0 my-4 overflow-x-auto break-all bg-transparent rounded-md"
                                    {...props}
                                  />
                                ),
                              }}
                            />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <DotPattern
                  cy={1}
                  cr={1}
                  width={20}
                  height={20}
                  x={10}
                  y={25}
                  cx={1}
                  className="fill-[#ffffff10] md:fill-[#ffffff10] absolute z-[1]"
                />
              </div>
            </div>
          </>
        )}

        {/* Footer */}

        {forkState == "fork" && (
          <>
            <div className="flex justify-end gap-3 p-3 md:p-6 border-t border-[#242424]">
              <button
                type="button"
                onClick={() => onOpenChange(false)}
                className="px-6 py-2 h-[40px] md:h-[48px] bg-transparent border border-[#333333] text-[#DCDCE5] hover:bg-[#FFFFFF0A] hover:border-[#444444] rounded-full font-[500] text-[16px]"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleFork}
                className="max-md:px-4 px-6 text-nowrap py-2 h-[40px] md:h-[48px] bg-white text-black hover:bg-gray-100 rounded-full font-[600] text-[14px] max-md:font-semibold  md:text-[16px] flex items-center gap-2"
              >
                <img src={Summarize} alt="Fork" className="w-6 h-6 invert" />
                <span>Proceed to Summarization</span>
              </button>
            </div>
          </>
        )}

        {forkState == "review" && (
          <>
            <div className="flex justify-end gap-3 p-3 md:p-6 border-t border-[#242424] bg-[#18181A] z-[10]">
              <button
                type="button"
                onClick={finalizeFork}
                disabled={isEditingMode || submitingFinal || !hasAnyContent}
                className="px-6 py-2 h-[40px] md:h-[48px] bg-white text-black hover:bg-gray-100 rounded-full font-[600] text-[16px] flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {submitingFinal ? (
                  <>
                    <TailChase size="20" speed="1.75" color="#000" />
                    Forking...
                  </>
                ) : (
                  "Approve & Start Forking"
                )}
                {!submitingFinal && (
                  <img src={ForkIcon} alt="Fork" className="w-6 h-6 invert" />
                )}
              </button>
            </div>
          </>
        )}

        {forkState == "summary" && (
          <>
            <div className="flex relative max-md:max-h-[80vh] md:min-w-[700px] flex-col max-w-[inherit] z-[10] h-[calc(100vh-150px)] mb-[24px]">
              <div className="flex justify-between z-[10] md:gap-[10px] flex-col md:p-6 pb-2 md:pb-4 p-4 bg-[#18181A] border-[#242424] border-b">
                <div className="flex items-center justify-between md:gap-[10px]">
                  <h2 className="text-[18px] md:text-[22px] font-medium text-white">
                    Session Summary
                  </h2>
                  <button
                    type="button"
                    onClick={() => {
                      setForkState("fork");
                      onOpenChange(false);
                    }}
                    className="w-6 h-6 flex items-center justify-center text-[#737780] hover:text-white transition-colors"
                    aria-label="Close modal"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
              </div>

              <div className="flex relative overflow-hidden flex-col h-full gap-4 max-md:w-[calc(95vw)]">
                <div className="flex flex-col w-full min-w-0 z-[10] gap-6 p-3 pt-6 overflow-hidden overflow-y-scroll md:p-6">
                  <div className="flex flex-col w-full min-w-0 gap-4 mb-[40px]">
                    {SUMMARY_DATA_ARRAY.map((item, index) => (
                      <div
                        key={index}
                        className="flex flex-col bg-[#17181A] border-[#242424] border text-wrap min-w-0 rounded-[10px] p-1"
                      >
                        <div className="flex items-center justify-between p-3 px-4 gap-2 bg-[#FFFFFF0F] backdrop-blur-lg rounded-[8px]">
                          <span className="text-[#FFFFFF] text-[16px] tracking-[-0.2px] font-medium capitalize leading-[20px]">
                            {item.tagName.replaceAll("_", " ")}
                          </span>
                        </div>

                        <div className="max-w-full p-3 px-4 overflow-hidden">
                          <StyledMarkdown
                            children={
                              item.content ||
                              "No content available for this section."
                            }
                            variant="compact"
                            className="break-words text-white/80 overflow-wrap-anywhere max-w-none"
                            customComponents={{
                              p: ({ node, ...props }) => (
                                <p
                                  className="font-medium break-words text-wrap font-['Inter'] leading-[28px]"
                                  {...props}
                                />
                              ),
                              pre: ({ ...props }) => (
                                <pre
                                  className="p-0 my-4 overflow-x-auto break-all bg-transparent rounded-md"
                                  {...props}
                                />
                              ),
                            }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                <DotPattern
                  cy={1}
                  cr={1}
                  width={20}
                  height={20}
                  x={10}
                  y={25}
                  cx={1}
                  className="fill-[#ffffff10] md:fill-[#ffffff10] absolute z-[1]"
                />
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};
