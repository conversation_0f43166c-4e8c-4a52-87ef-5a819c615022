import axios, { AxiosInstance } from "axios";
import { supabase } from "../lib/supabase";
import { config } from "../config";
import { store } from "../store";
import { setTabs, setActiveTab, cleanupTabStates } from "../store/tabSlice";
import { cleanupTabStatesInLocalStorage } from "../utils/tabStateCleanup";
import { cleanupUnusedStores } from "../components/TabContent";
import { clearAllStorageSafely } from "../lib/utils/modalStateManager";

// Function to create a new axios instance with interceptors
function createAxiosInstance(baseURL: string): AxiosInstance {
  const instance = axios.create({
    baseURL,
    headers: {
      "Content-Type": "application/json",
    },
  });

  // Add auth interceptor
  instance.interceptors.request.use(async (config) => {
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (session?.access_token) {
      config.headers.Authorization = `Bearer ${session.access_token}`;
    }
    return config;
  });

  // Add request interceptor for logging
  instance.interceptors.request.use((config) => {
    return config;
  });

  // Simple response interceptor - handle 401s and log responses
  instance.interceptors.response.use(
    (response) => {
      // //console.log("API Response:", {
      //   status: response.status,
      //   data: response.data,
      //   url: response.config.url,
      // });
      return response;
    },
    async (error) => {
      console.error("API Error:", {
        status: error.response?.status,
        data: error.response?.data,
        url: error.config?.url,
        message: error.message,
      });
      if (
        error.response?.status === 401 &&
        window.location.pathname !== "/login"
      ) {
        try {
          // First clean up any stale tab states in localStorage
          cleanupTabStatesInLocalStorage();

          // Reset tab state to only include home tab
          store.dispatch(setTabs([{
            id: "home",
            title: "Home",
            path: "/"
          }]));

          // Set home as active tab
          store.dispatch(setActiveTab("home"));

          // Clean up any tab states that don't have corresponding tabs
          store.dispatch(cleanupTabStates());

          // Clean up any unused tab stores
          cleanupUnusedStores(new Set(["home"]));

          // Now sign out and clear storage safely
          // await supabase.auth.signOut();
          clearAllStorageSafely();

          window.location.href = '/login';
        } catch (err) {
          console.error("Error during 401 logout cleanup:", err);
          // Still attempt to sign out even if cleanup fails
          // await supabase.auth.signOut();
          clearAllStorageSafely();

          window.location.href = '/login';
        }
      }
      return Promise.reject(error);
    },
  );

  return instance;
}

// Create initial axios instance
let api = createAxiosInstance(config.apiBaseUrl);

// Function to reinitialize the API with a new base URL
export function reinitializeApi(baseURL: string) {
  api = createAxiosInstance(baseURL);
}

import ipc from "../lib/ipc/client";
import { EnvValues } from "@/store/deploySlice";
import { ResponseImageData } from "@/types/message";

// Define the step interface based on the API response
export interface DeploymentStep {
  id: string;
  run_id: string;
  name: string;
  status: "success" | "running" | "failed" | "pending";
  error: string;
  logs: string[];
  retries: number;
  created_at: string;
  updated_at: string;
}

// Define the deployment run interface
export interface DeploymentRun {
  run_id: string;
  status: "running" | "success" | "failed";
  steps: DeploymentStep[];
  deploy_url: string;
}

// Define the deployment status response interface
export interface DeployStatusResponse {
  deployed_run_id: string;
  status: "running" | "success" | "failed" | "not_deployed";
  deploy_url: string;
  app_name: string;
  latest_run: DeploymentRun;
  custom_domain: {
    domain: string;
    status: "verified" | "pending" | "failed";
    dns_records: {
      type: string;
      name: string;
      value: string;
    };
  } | null;
}

// Define the deployment run params interface
export interface DeploymentRunParams {
  cpu: string;
  image: string;
  memory: string;
  replicas: number;
}

// Define the deployment history run interface
export interface DeploymentHistoryRun {
  id: string;
  type: string;
  app: string;
  user_id: string;
  source_pod: string;
  source_namespace: string;
  status: "running" | "success" | "failed" | "pending";
  params: DeploymentRunParams;
  created_at: string;
  updated_at: string;
}

// Define the deployment history response interface
export interface DeploymentHistoryResponse {
  deployed_run_id: string;
  runs: DeploymentHistoryRun[];
}

// Define domain status interface
export interface DomainStatus {
  domain?: string;
  status?: 'not_connected' | 'pending_verification' | 'connected' | 'success';
  dns_records?: {
    type: string;
    name: string;
    value: string;
  }[];
  error?: string;
  // New fields from the API response
  message?: string;
  ip_address?: string;
  verification_required?: boolean;
}

export interface PreviewURLResponse { preview_url: string; vscode_url: string; password: string, base_preview_url: string }

// Port mapping interface for job payload
export interface PortMapping {
  id: string;
  hostPort: string;
  containerPort: string;
  service: string;
}

// Job payload interface
export interface JobPayload {
  processor_type: string;
  is_cloud: boolean;
  env_image: string;
  branch: string;
  repository: string;
  prompt_name: string;
  prompt_version: string;
  work_space_dir: string;
  task: string;
  model_name: string;
  per_instance_cost_limit: number;
  agentic_skills: string[];
  portMapping: PortMapping[];
  plugin_version: string;
  human_timestamp: number;
  chat_mode: string;
  is_template_enabled: boolean;
  lazy_llm_call_enabled: boolean;
  is_pvc_enabled: boolean;
  mock_llm: boolean;
  agent_name: string;
  source_repo: {
    branch: string;
    repo: string;
    owner: string;
    provider: string;
  };
  target_repo: {
    branch: string;
    repo: string;
    owner: string;
    provider: string;
  };
  forked:{
    parent_job_id: string | null;
    parent_job_title: string | null;
  },
  base64_image_list: ResponseImageData[];
  original_task: string;
  container_id: string;
  initial_commit_id: string;
  dynamic_preview_subdomain: string;
  forked_from: string | null;
}

// Job response interface
export interface JobResponse {
  id: string;
  status: "PENDING" | "RUNNING" | "COMPLETED" | "FAILED" | "CANCELLED";
  payload: JobPayload;
  created_at: string;
  updated_at: string;
  created_by: string;
  client_ref_id: string;
  request_id: string | null;
  is_public: boolean;
  state: "ENV_CREATED" | "STARTED" | "COMPLETED" | "FAILED" | "CANCELLED";
  
}


// Define the deployment logs response interface
export interface DeployLogsResponse {
  build: string[];
  deploy: string[];
  manage_secrets: string[];
  mongodb_migrate: string[];
  health_check: string[];
}

// Define the deployment logs response interface
export interface DeployLogsResponse {
  build: string[];
  deploy: string[];
  manage_secrets: string[];
  mongodb_migrate: string[];
  health_check: string[];
}

export interface CreditsBalance {
  error?: string;
  ecu_balance: number;
  monthly_credits_balance: number;
  top_up_credit_balance: number;
  monthly_credits_refresh_date: string;
  subscription: {
    id: string;
    name: string;
    status: "active" | "past_due" | "unpaid" | "initiated_cancellation" | 'free';
    expires_at: boolean;
    monthly_credit_limit: number;
  };
}


export const agentApi = {
  getRepositories: async () => {
    //console.log("agentApi: Fetching repositories (Docker images)");
    const images = await ipc.listImages();
    //console.log("agentApi: Received images:", images);
    return { data: images };
  },
  getPrompts: () => api.get("/prompts"),
  getJobs: () => api.get("/jobs/?limit=50"),
  getJob: async (jobId: string): Promise<
    {
      data: JobResponse;
      error?: string;
    }> => api.get(`/jobs/v0/${jobId}/`),
  getActions: (params: { job_id: string }) => api.get("/actions/", { params }),
  pauseJob: (data: { job_id: string; agent_name?: string }) =>
    api.post("/actions/pause/", {
      job_id: data.job_id,
      agent_type: data.agent_name,
    }),
  resumeJob: (data: { job_id: string; message?: string }) =>
    api.post("/jobs/resume", data),
  stopJob: (data: { job_id: string }) => api.post("/jobs/stop", data),
  // Budget API methods
  getBudget: async (jobId: string) => {
    const response = await api.get(`/budget/${jobId}`);
    if (!response.data.success) {
      throw new Error(response.data.data.error);
    }
    return response.data.data;
  },

  pauseEnvironment: async (jobId: string) => {
    const response = await api.post(`/jobs/v0/${jobId}/pause-environment`);
    if (!response.data.success) {
      throw new Error(response.data.data.error);
    }
    return response.data.data;
  },

  restartEnvironment: async (jobId: string) => {
    const response = await api.post(`/jobs/v0/${jobId}/restart-environment`);

    if (response.status != 200) {
      throw new Error(response.data.error);
    }
    console.log("agentApi: Environment restarted successfully", response);
    return response.data;
  },

  updateBudget: async (jobId: string, amount: number) => {
    const response = await api.put(`/budget/${jobId}`, {
      amount,
    });
    if (!response.data.success) {
      throw new Error(response.data.data.error);
    }
    return response.data.data;
  },


  // Skills API methods
  getSkills: async () => {
    try {
      const data = await api.get("/skills/");
      return data;
    } catch (error: any) {
      console.error("Error fetching skills:", error);
      return { data: [], error: error.message };
    }
  },

  // Config API methods
  getConfig: (appVersion: string = "1.0.0") => {
    return api
      .get("/repositories/config", {
        params: { app_version: appVersion },
      })
      .then((response) => response.data);
  },

  // Trajectory API methods
  getTrajectory: (jobId: string, token?: string, lastRequestId?: string) => {
    const headers = token ? { Authorization: `Bearer ${token}` } : undefined;
    const params = new URLSearchParams({ job_id: jobId });
    if (lastRequestId) {
      params.append("last_request_id", lastRequestId);
    }
    return api.get(`/trajectories/v0/?${params.toString()}`, { headers });
  },

  // Cloud job submission
  submitCloudJob: async (
    payload: any,
    client_ref_id: string | undefined,
    resume: boolean | undefined = false,
  ) => {
    try {
      const data: any = {
        client_ref_id: client_ref_id,
        payload: {
          processor_type: "env_only",
          is_cloud: true,
          env_image: payload.env_image,
          branch: "",
          repository: "",
          ...payload,
        },
        model_name: payload.model_name,
        resume: resume,
      };

      if (resume) {
        data.id = client_ref_id;
      }

      if (!client_ref_id) {
        throw new Error("Either job_id or client_ref_id must be provided");
      }
      const response = await api.post("/jobs/v0/submit-queue/", data);
      return response.data;

    } catch (error) {
      console.error("Error submitting cloud job:", error);
      throw error;
    }
  },

  // Get job status by client reference ID
  getJobByClientRef: async (clientRefId: string) => {
    try {
      const response = await api.get(`/jobs/v0/ref/${clientRefId}`);
      return response.data;
    } catch (error: any) {
      // Return null for 404, which is expected initially
      // if (error.response?.status === 404) {
      //   return null;
      // }
      console.error("Error getting job by client ref:", error);
      return {};
      // throw error;
    }
  },

  // Get job preview URL
  getJobPreviewUrl: async (jobId: string): Promise<PreviewURLResponse> => {
    try {
      const response = await api.get(`/jobs/v0/${jobId}/preview`);
      return response.data;
    } catch (error) {
      console.error("Error getting job preview URL:", error);
      throw error;
    }
  },

  // Get git diff from cloud API
  getCloudGitDiff: async (jobId: string, filePath?: string, initialCommitId?: string) => {
    return api.get(`/jobs/v0/diff/${jobId}`, {
      params: {
        file_path: filePath || "",
        initial_commit_id: initialCommitId || "",
      },
    });
  },

  // Get user credits balance
  getCreditsBalance: async (): Promise<CreditsBalance> => {
    try {
      const response = await api.get('/credits/balance');
      return response.data;
    } catch (error: any) {
      console.error("Error fetching credits balance:", error);
      return {
        error: error.message,
        ecu_balance: 0,
        top_up_credit_balance: 0,
        monthly_credits_balance: 0,
        monthly_credits_refresh_date: "",
        subscription: {
          id: "",
          name: "",
          status: "free",
          expires_at: false,
          monthly_credit_limit: 5,
        }
      };
    }
  },
  // Rollback to a previous state in cloud flow
  rollbackToRequestId: async (jobId: string, requestId: string, agentName: string = 'EmergentAssistant', rollbackType: 'all' | 'messages' = 'all', isRetry: boolean = false) => {
    try {

      const params: any = {
        job_id: jobId,
        request_id: requestId,
        base_url: config.apiBaseUrl,
        agent_name: agentName
      }

      if (rollbackType === 'messages') {
        params.checkout = false;
      }

      if (rollbackType === 'all') {
        params.checkout = true;
      }

      if (isRetry) {
        params.retry = true;
      }

      const response = await api.post('/jobs/v0/rollback/cloud/', params);
      return response.data;
    } catch (error) {
      console.error("Error rolling back to previous state:", error);
      throw error;
    }
  },
  // Get user details including GitHub installations
  getUserDetails: async () => {
    try {
      const response = await api.get('/user/details');
      return response.data;
    } catch (error: any) {
      console.error("Error fetching user details:", error);
      return { id: null, github_installations: [], error: error.message };
    }
  },

  getGitHubInstallations: async () => {
    try {
      const response = await api.get('/github/installations');
      return response.data;
    } catch (error: any) {
      console.error("Error fetching GitHub installations:", error);
      return { installations: [], error: error.message };
    }
  },

  saveGitHubInstallation: async (installationId: string, code: string) => {
    try {
      const response = await api.post('/github/installation', {
        installation_id: installationId,
        code
      });
      return response.data;
    } catch (error: any) {
      console.error("Error saving GitHub installation:", error);
      return { success: false, error: error.message };
    }
  },

  getGitHubRepositories: async () => {
    try {
      const response = await api.get('/github/repositories');
      return response.data;
    } catch (error: any) {
      console.error("Error fetching GitHub repositories:", error);
      return { repositories: [], error: error.message };
    }
  },

  getGitHubBranches: async (account_login: string, repo_name: string) => {
    try {
      // URL encode both account_login and repository name to handle special characters like dots
      const encodedAccountLogin = encodeURIComponent(account_login);
      const encodedRepoName = encodeURIComponent(repo_name);
      const response = await api.get(`/github/branches/${encodedAccountLogin}/${encodedRepoName}`);
      // The API might return the branches directly as an array
      return Array.isArray(response.data) ? response.data : response.data;
    } catch (error: any) {
      console.error("Error fetching GitHub branches:", error);
      return { branches: [], error: error.message };
    }
  },

  createGitHubRepository: async (data: {
    installation_id: string;
    org?: string | undefined;
    name: string;
  }) => {
    try {
      const response = await api.post('/github/repositories', data);
      return response.data;
    } catch (error) {
      console.error("Error creating GitHub repository:", error);
      throw error;
    }
  },

  pushToGitHub: async (jobId: string, data: {
    account_login: string;
    repo_name: string;
    branch_name: string;
    is_new_repo?: boolean;
    force?: boolean;
  }) => {
    try {
      const response = await api.post(`/jobs/v0/push_to_github/${jobId}`, data);
      return response.data;
    } catch (error: any) {
      console.error('Error pushing to GitHub:', error);

      // Only treat actual conflicts as conflict errors
      if (error.response &&
        (error.response.data?.detail?.toLowerCase?.()?.includes('updates were rejected because the remote contains work that you do not have locally'))) {
        // Use the branch name in the error message
        const branchName = data.branch_name || 'main';
        const errorMessage = `Conflict Detected\nYour changes conflict with the remote branch '${branchName}'. We recommend creating a new branch and pushing changes to that instead.`;
        throw new Error(errorMessage);
      }

      // For all other errors, provide a more detailed error message
      if (error.response) {
        const statusCode = error.response.status;
        let errorData;

        try {
          // Handle different error data formats
          if (error.response.data?.error) {
            errorData = typeof error.response.data.error === 'object'
              ? JSON.stringify(error.response.data.error)
              : error.response.data.error;
          } else if (error.response.data) {
            errorData = typeof error.response.data === 'object'
              ? JSON.stringify(error.response.data)
              : error.response.data;
          } else {
            errorData = error.message || 'Unknown error';
          }
        } catch (e) {
          errorData = 'Error parsing error data';
        }

        const errorMessage = `Error (${statusCode}): ${errorData}`;
        throw new Error(errorMessage);
      }

      // If no response object is available, throw the original error
      throw error;
    }
  },
  // Get rollback status for a cloud job
  getRollbackStatus: async (jobId: string, requestId?: string) => {
    try {
      const urlPath = requestId ? `/${jobId}/rollback/status?request_id=${requestId}` : `/${jobId}/rollback/status`;
      const response = await api.get(urlPath);
      return response.data;
    } catch (error) {
      console.error("Error getting rollback status:", error);
      throw error;
    }
  },
  // Get trajectory stream for a job
  getTrajectoryStream: async (jobId: string, lastRequestId?: string) => {
    try {
      //console.log('[agentApi] Starting trajectory stream for job:', jobId, 'lastRequestId:', lastRequestId);
      const params = new URLSearchParams();
      params.append("job_id", jobId);
      // if (lastRequestId) {
      //   params.append("last_request_id", lastRequestId);
      // }
      const url = `${config.apiBaseUrl}/trajectories/v0/stream?${params.toString()}`
      //console.log('[agentApi] Stream URL:', url);
      return await api.get(url, {
        headers: {
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache',
        },
        responseType: 'stream',
        adapter: 'fetch'
      })
    } catch (error) {
      console.error("Error starting trajectory stream:", error);
      throw error;
    }
  },
  fetchTrajectory: async (jobId: string, lastRequestId?: string, options?: {
    onTrajectoryResult?: (data: any) => void;
    onCurrentChunkResult?: (data: any) => void;
    cache?: { current: { trajectory: any[] } };
  }) => {
    // Create URL parameters
    const params = new URLSearchParams();
    params.append('job_id', jobId);
    if (lastRequestId) {
      params.append('last_request_id', lastRequestId);
    }

    // Create the URL
    const url = `${config.apiBaseUrl}/trajectories/v0/stream?${params.toString()}`;
    //console.log('[agentApi] Fetching trajectory stream:', url);

    try {
      // Use the axios instance which already handles authentication
      const response = await api.get(url, {
        headers: {
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        },
        responseType: 'stream',
        adapter: 'fetch'
      });

      if (!response.data) {
        throw new Error('Failed to fetch trajectory: No data returned');
      }

      // Process the stream in real-time
      const reader = response.data.getReader();
      if (!reader) {
        throw new Error('Stream reader could not be created');
      }

      // Collect all trajectory data to return at the end
      let completeData = { trajectories: { data: [], updated_data: [], agent_status: false, chat_mode: null } };
      let buffer = ''; // Buffer for incomplete chunks

      // Process the stream until done
      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          // Process any remaining data in the buffer
          if (buffer.trim()) {
            try {
              const lines = buffer.split('\n');
              for (const line of lines) {
                if (line.startsWith('data: ')) {
                  const data = line.substring(6);
                  const parsed = JSON.parse(data);

                  if (parsed.trajectories?.data) {
                    completeData = parsed;
                  }
                }
              }
            } catch (error) {
              console.error('[AgentApi] Error processing remaining buffer:', error);
            }
          }
          break;
        }

        // Convert the chunk to text and add to buffer
        const chunk = new TextDecoder().decode(value);
        buffer += chunk;

        // Process complete lines in the buffer
        const lines = buffer.split('\n');
        // Keep the last line in the buffer if it's incomplete
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              // Extract the JSON payload by removing the 'data: ' prefix
              const data = line.substring(6); // 'data: '.length = 6
              const parsed = JSON.parse(data);

              // If we have trajectory data, use it and we have callbacks
              if (parsed.trajectories?.data && options?.onTrajectoryResult && options?.cache) {
                // Process the trajectory data immediately
                const sortedNewItems = parsed.trajectories.data
                  .sort((a: any, b: any) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
                  .map((item: any) => {
                    return {
                      ...item.traj_payload,
                      id: item.id,
                      request_id: item.request_id,
                      job_id: item.job_id,
                      traj_payload: item.traj_payload,
                      commit_id: item.commit_id,
                      parent_request_id: item.parent_request_id,
                      status: item.status,
                      created_at: item.created_at,
                      updated_at: item.updated_at
                    };
                  });

                // Filter out items that already exist in the cache
                const newItems = sortedNewItems.filter((payload: any) => {
                  const exists = options?.cache?.current.trajectory.some((t: any) => t.id === payload.id);
                  return !exists;
                });

                // Update the cache with new items
                if (newItems.length > 0 && options?.cache && options?.onTrajectoryResult) {
                  options.cache.current.trajectory = [...options.cache.current.trajectory, ...newItems];
                  options.onTrajectoryResult(options.cache.current.trajectory);
                }

                // Update completeData for the function return value
                completeData = parsed;
              } else if (parsed.trajectories?.data) {
                // Just update completeData if we don't have callbacks
                completeData = parsed;
              }

              if (parsed.chunk && options?.onCurrentChunkResult && options?.cache) {
                // Check if this chunk's request_id is already in the trajectory data
                const isExistingChunk = options.cache.current.trajectory.some(
                  (item: any) => item.request_id === parsed.chunk.request_id
                ) && parsed.chunk.completed;

                if (isExistingChunk) {
                  // We already have this chunk's data, so skip it
                  options.onCurrentChunkResult("");
                } else {
                  options.onCurrentChunkResult(parsed);
                }
              }
            } catch (error) {
              console.error('[AgentApi] Error parsing SSE data:', error);
              // Don't break on parsing errors - just continue with the next line
            }
          }
        }
      }
      return completeData;
    } catch (error) {
      console.error('[AgentApi] Error fetching trajectory stream:', error);
      throw error;
    }
  },

  handleHimMessage: async (jobId: string, message: string) => {
    try {
      const response = await api.post(`/jobs/v0/him/`, {
        message,
        "job_id": jobId
      });
      return response.data;
    } catch (error) {
      console.error("Error handling HIM message:", error);
      throw error;
    }
  },

  createCheckoutSession: async (amount: number, isSubscription: boolean = false, planId?: string) => {
    try {
      const payload: any = {
        currency: "usd",
        metadata: {
          hostname: `${window.location.origin}`
        }
      };

      if (isSubscription && planId) {
        payload.plan_id = planId;
        payload.mode = "subscription";
      } else {
        payload.amount = amount;
      }

      const response = await api.post('/payments/checkout-sessions', payload);
      if (!response.data.sessionId || !response.data.url) {
        throw new Error('Invalid response from checkout session creation');
      }
      return response.data;
    } catch (error) {
      console.error("Error creating checkout session:", error);
      throw error;
    }
  },

  createSubscriptionCheckout: async (priceId: string) => {
    try {
      const response = await api.post('/payments/subscription', {
        price_id: priceId,
        metadata: {
          hostname: `${window.location.origin}`
        }
      });
      if (!response.data.sessionId || !response.data.url) {
        throw new Error('Invalid response from subscription checkout creation');
      }
      return response.data;
    } catch (error) {
      console.error("Error creating subscription checkout:", error);
      throw error;
    }
  },

  checkPaymentStatus: async (paymentId: string) => {
    try {
      const response = await api.get(`/payments/${paymentId}/status`);
      return response.data;
    } catch (error) {
      console.error("Error checking payment status:", error);
      throw error;
    }
  },

  getManageSubscriptionUrl: async () => {
    try {
      const response = await api.get('/payments/manage-subscription');
      return response.data;
    } catch (error) {
      console.error("Error getting subscription management URL:", error);
      throw error;
    }
  },

  // Get debug information from LLM proxy
  getDebugInfo: async (runId: string, stepNum?: number) => {
    try {
      let url = `/llm-proxy/query/${runId}`;
      if (stepNum !== undefined) {
        url += `?step_number=${stepNum}`;
      }
      const response = await api.get(url);
      return response.data;
    } catch (error) {
      console.error("Error fetching debug information:", error);
      throw error;
    }
  },

  // Chat History API methods
  getChatHistory: async (chatId: string) => {
    try {
      const response = await api.get(`/chat-history/v0/${chatId}`, {
        timeout: 1200000
      });
      return response.data;
    } catch (error) {
      console.error("Error fetching chat history:", error);
      throw error;
    }
  },

  updateChatHistory: async (chatId: string, updateData: {
    chat_history?: any[];
    current_observation?: string;
    iteration_number?: number;
  }) => {
    try {
      const response = await api.patch(`/chat-history/v0/${chatId}`, updateData);
      return response.data;
    } catch (error) {
      console.error("Error updating chat history:", error);
      throw error;
    }
  },

  deployApp: async (jobId: string, image?: string, deployment_id?: string) => {
    try {
      const response = await api.post(`/jobs/v0/deploy`, {
        job_id: jobId,
        image: image,
        deployment_id: deployment_id
      });
      return response.data;
    } catch (error: any) {
      console.error("Error deploying app:", error);
      // Extract error message if available
      if (error.response && error.response.data) {
        throw new Error(error.response.data.detail || error.response.data.error || "Failed to deploy app");
      }
      throw error;
    }
  },
  getDeployStatus: async (jobId: string): Promise<DeployStatusResponse> => {
    try {
      const response = await api.get(`/jobs/v0/deploy/${jobId}/latest`);
      return response.data;
    } catch (error: any) {
      console.error("Error getting deploy status:", error);
      if (error.response && error.response.data) {
        throw new Error(error.response.data.detail || error.response.data.error || "Failed to deploy app");
      }
      throw error;
    }
  },

  getDeployLogs: async (jobId: string): Promise<DeployLogsResponse> => {
    try {
      const response = await api.get(`/jobs/v0/deploy/${jobId}/logs`);
      return response.data;
    } catch (error: any) {
      console.error("Error getting deploy logs:", error);
      if (error.response && error.response.data) {
        throw new Error(error.response.data.detail || error.response.data.error || "Failed to get deploy logs");
      }
      throw error;
    }
  },

  getDeploymentHistory: async (jobId: string): Promise<DeploymentHistoryResponse> => {
    try {
      const response = await api.get(`/jobs/v0/deploy/${jobId}/history`);
      return response.data;
    } catch (error: any) {
      console.error("Error getting deployment history:", error);
      if (error.response && error.response.data) {
        throw new Error(error.response.data.detail || error.response.data.error || "Failed to deploy app");
      }
      throw error;
    }
  },
  getEnvs: async (jobId: string): Promise<{
    envs: {
      [key: string]: string;
    }
  }> => {
    try {
      const response = await api.get(`/jobs/v0/deploy/${jobId}/envs`);
      return response.data;
    } catch (error: any) {
      console.error("Error getting envs:", error);
      if (error.response && error.response.data) {
        throw new Error(error.response.data.detail || error.response.data.error || "Failed to deploy app");
      }
      throw error;
    }
  },
  shutDownDeployment: async (jobId: string) => {
    try {
      const response = await api.delete(`/jobs/v0/deploy/${jobId}`);
      return response.data;
    } catch (error: any) {
      console.error("Error shutting down deployment:", error);
      if (error.response && error.response.data) {
        throw new Error(error.response.data.detail || error.response.data.error || "Failed to deploy app");
      }
      throw error;
    }
  },
  deploymentHistory: async (jobId: string) => {
    try {
      const response = await api.get(`/jobs/v0/deploy/${jobId}/history`);
      return response.data;
    } catch (error: any) {
      console.error("Error fetching deployment history:", error);
      if (error.response && error.response.data) {
        throw new Error(error.response.data.detail || error.response.data.error || "Failed to deploy app");
      }
      throw error;
    }
  },
  updateEnvironmentVariables: async (jobId: string, envs: EnvValues[]) => {
    try {
      const response = await api.put(`/jobs/v0/deploy/${jobId}/envs`,
        envs.reduce((acc, env) => {
          acc[env.key] = env.value;
          return acc;
        }, {} as { [key: string]: string })
      );
      return response.data;

    } catch (error: any) {
      console.error("Error updating environment variables:", error);
      if (error.response && error.response.data) {
        throw new Error(error.response.data.detail || error.response.data.error || "Failed to deploy app");
      }
      throw error;
    }
  },

  // Domain management API methods

  registerDomain: async (domain: string, jobId: string): Promise<DomainStatus> => {
    try {
      const response = await api.post('/jobs/v0/domains/register', { domain: domain, job_id: jobId });
      return response.data;
    } catch (error: any) {
      console.error("Error registering domain:", error);
      if (error.response && error.response.data) {
        throw new Error(error.response.data.detail || error.response.data.error || "Failed to register domain");
      }
      throw error;
    }
  },

  verifyDomain: async (domain: string, jobId: string): Promise<{
    status: string;
    message: string;
    verified: boolean;
    domain?: string;
    dns_records?: {
      type: string;
      name: string;
      value: string;
    }[];
  }> => {
    try {
      const response = await api.post('/jobs/v0/domains/verify', { domain, job_id: jobId });
      return response.data;
    } catch (error: any) {
      console.error("Error verifying domain:", error);
      if (error.response && error.response.data) {
        throw new Error(error.response.data.detail || error.response.data.error || "Failed to verify domain");
      }
      throw error;
    }
  },

  connectDomain: async (domain: string, jobId: string): Promise<DomainStatus> => {
    try {
      const response = await api.post('/jobs/v0/domains/connect', {
        domain,
        job_id: jobId
      });
      return response.data;
    } catch (error: any) {
      console.error("Error connecting domain to app:", error);
      if (error.response && error.response.data) {
        throw new Error(error.response.data.detail || error.response.data.error || "Failed to connect domain to app");
      }
      throw error;
    }
  },

  getDomainStatus: async (domain: string): Promise<DomainStatus> => {
    try {
      const response = await api.get(`/jobs/v0/domains/status?domain=${encodeURIComponent(domain)}`);
      return response.data;
    } catch (error: any) {
      console.error("Error getting domain status:", error);
      if (error.response && error.response.data) {
        throw new Error(error.response.data.detail || error.response.data.error || "Failed to get domain status");
      }
      throw error;
    }
  },

  unlinkDomain: async (domain: string, jobId: string): Promise<DomainStatus> => {
    try {
      const response = await api.delete(`/jobs/v0/domains/${encodeURIComponent(domain)}/apps/${jobId}`);
      return response.data;
    } catch (error: any) {
      console.error("Error unlinking domain:", error);
      if (error.response && error.response.data) {
        throw new Error(error.response.data.detail || error.response.data.error || "Failed to unlink domain");
      }
      throw error;
    }
  },

  getDeployments: async (): Promise<Deployment[] | { error: string }> => {
    try {
      const response = await api.get('/jobs/v0/deployments');
      return response.data;
    } catch (error: any) {
      console.error("Error fetching deployments:", error);
      return { error: error.message };
    }
  },

  // Fork Related API Calls

  generateSummary : async (jobId: string): Promise<any> => {
    try {
      const response = await api.post(`/jobs/v0/${jobId}/summary`);
      return response.data;
    } catch (error: any) {
      console.error("Error generating summary:", error);
      if (error.response && error.response.data) {}
      throw error;
    }
  },

  viewSummary: async (jobId: string): Promise<any> => {
    try {
      const response = await api.get(`/jobs/v0/${jobId}/summary`);
      return response.data;
    } catch (error: any) {
      console.error("Error viewing summary:", error);
      if (error.response && error.response.data) {}
      throw error;
    }
  },

  submitFork: async (jobId: string, summaryData: string): Promise<any> => {
    try {
      const response = await api.post(`/jobs/v0/${jobId}/fork-environment`, {
        summary: summaryData
      });
      return response.data;
    } catch (error: any) {
      console.error("Error submitting fork:", error);
      if (error.response && error.response.data) {
        throw new Error(error.response.data.detail || error.response.data.error || "Failed to submit fork");
      }
      throw error;
    }
  }
};


interface Deployment {
  job_id: string;
  app_name: string;
  deployed_url: string;
  custom_domain: string;
  status: "running" | "success" | "failed";
  deployed_at: string;
  deployment_id: string;
}