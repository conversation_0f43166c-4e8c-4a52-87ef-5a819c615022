/**
 * Utility functions for managing modal states in localStorage
 * These states should persist across sessions and not be cleared during logout
 *
 * IMPORTANT: Always use clearLocalStorageSafely() or clearAllStorageSafely()
 * instead of localStorage.clear() to preserve modal states across the app.
 */

const MODAL_STORAGE_KEY = 'emergent_modal_states';

interface ModalStates {
  hasSeenFeatureModal?: boolean;
  hasSeenWelcomeModal?: boolean;
  hasSeenForkIntro?: boolean;
  // Add other modal states here as needed
}

/**
 * Get all modal states from localStorage
 */
export function getModalStates(): ModalStates {
  try {
    const stored = localStorage.getItem(MODAL_STORAGE_KEY);
    return stored ? JSON.parse(stored) : {};
  } catch (error) {
    console.error('Error reading modal states from localStorage:', error);
    return {};
  }
}

/**
 * Set a specific modal state
 */
export function setModalState(modalKey: keyof ModalStates, value: boolean): void {
  try {
    const currentStates = getModalStates();
    const updatedStates = {
      ...currentStates,
      [modalKey]: value
    };
    localStorage.setItem(MODAL_STORAGE_KEY, JSON.stringify(updatedStates));
  } catch (error) {
    console.error('Error saving modal state to localStorage:', error);
  }
}

/**
 * Get a specific modal state
 */
export function getModalState(modalKey: keyof ModalStates, defaultValue: boolean = false): boolean {
  const states = getModalStates();
  return states[modalKey] ?? defaultValue;
}

/**
 * Check if user has seen the feature modal
 */
export function hasSeenFeatureModal(): boolean {
  return getModalState('hasSeenFeatureModal', false);
}

/**
 * Mark feature modal as seen
 */
export function markFeatureModalAsSeen(): void {
  setModalState('hasSeenFeatureModal', true);
}

/**
 * Check if user has seen the welcome modal
 */
export function hasSeenWelcomeModal(): boolean {
  return getModalState('hasSeenWelcomeModal', false);
}

/**
 * Mark welcome modal as seen
 */
export function markWelcomeModalAsSeen(): void {
  setModalState('hasSeenWelcomeModal', true);
}

/**
 * Check if user has seen the fork intro tooltip
 */
export function hasSeenForkIntro(): boolean {
  return getModalState('hasSeenForkIntro', false);
}

/**
 * Mark fork intro tooltip as seen
 */
export function markForkIntroAsSeen(): void {
  setModalState('hasSeenForkIntro', true);
}

/**
 * Reset all modal states (useful for testing or admin purposes)
 */
export function resetModalStates(): void {
  try {
    localStorage.removeItem(MODAL_STORAGE_KEY);
  } catch (error) {
    console.error('Error resetting modal states:', error);
  }
}

/**
 * Reset feature modal state specifically (for testing)
 */
export function resetFeatureModalState(): void {
  setModalState('hasSeenFeatureModal', false);
}

/**
 * Reset fork intro state specifically (for testing)
 */
export function resetForkIntroState(): void {
  setModalState('hasSeenForkIntro', false);
}

/**
 * Migrate old localStorage keys to the new modal state system
 * This should be called once during app initialization
 */
export function migrateOldModalStates(): void {
  try {
    // Migrate old hasSeenForkIntro key
    const oldForkIntro = localStorage.getItem('hasSeenForkIntro');
    if (oldForkIntro === 'true' && !hasSeenForkIntro()) {
      markForkIntroAsSeen();
      localStorage.removeItem('hasSeenForkIntro'); // Clean up old key
    }

    // Add other migrations here as needed
    // Example:
    // const oldWelcomeModal = localStorage.getItem('hasSeenWelcomeModal');
    // if (oldWelcomeModal === 'true' && !hasSeenWelcomeModal()) {
    //   markWelcomeModalAsSeen();
    //   localStorage.removeItem('hasSeenWelcomeModal');
    // }
  } catch (error) {
    console.error('Error during modal state migration:', error);
  }
}

/**
 * Safely clear localStorage while preserving modal states
 * Use this instead of localStorage.clear() to maintain modal preferences
 */
export function clearLocalStorageSafely(): void {
  try {
    // Preserve modal states before clearing
    const modalStates = preserveModalStates();

    // Clear localStorage
    localStorage.clear();

    // Restore modal states after clearing
    restoreModalStates(modalStates);
  } catch (error) {
    console.error('Error during safe localStorage clearing:', error);
    // Fallback to regular clear if something goes wrong
    localStorage.clear();
  }
}

/**
 * Safely clear both localStorage and sessionStorage while preserving modal states
 * Use this for complete storage cleanup (like logout scenarios)
 */
export function clearAllStorageSafely(): void {
  try {
    // Preserve modal states before clearing
    const modalStates = preserveModalStates();

    // Clear both storages
    localStorage.clear();
    sessionStorage.clear();

    // Restore modal states after clearing
    restoreModalStates(modalStates);
  } catch (error) {
    console.error('Error during safe storage clearing:', error);
    // Fallback to regular clear if something goes wrong
    localStorage.clear();
    sessionStorage.clear();
  }
}

// Expose reset functions globally for easy testing in console
if (typeof window !== 'undefined') {
  (window as any).resetFeatureModal = resetFeatureModalState;
  (window as any).resetForkIntro = resetForkIntroState;
  (window as any).resetAllModals = resetModalStates;
  (window as any).clearStorageSafely = clearLocalStorageSafely;
  (window as any).clearAllStorageSafely = clearAllStorageSafely;
}

/**
 * Preserve modal states during localStorage clearing
 * Call this before localStorage.clear() to backup modal states
 */
export function preserveModalStates(): ModalStates | null {
  try {
    return getModalStates();
  } catch (error) {
    console.error('Error preserving modal states:', error);
    return null;
  }
}

/**
 * Restore modal states after localStorage clearing
 * Call this after localStorage.clear() to restore modal states
 */
export function restoreModalStates(states: ModalStates | null): void {
  if (!states) return;
  
  try {
    localStorage.setItem(MODAL_STORAGE_KEY, JSON.stringify(states));
  } catch (error) {
    console.error('Error restoring modal states:', error);
  }
}
