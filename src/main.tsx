import React from "react";
import ReactDOM from "react-dom/client";
import { <PERSON><PERSON>er<PERSON>outer } from "react-router-dom";
import App from "./App";
import { Provider } from "react-redux";
import { store, persistor } from "./store";
import { ThemeProvider } from "./contexts/ThemeContext";
import { LoadingProvider, AuthProvider, CreditsProvider } from "./contexts";
import { TabProvider } from "./components/TabBar";
import { PersistGate } from "redux-persist/integration/react";
import { PostHogProvider } from 'posthog-js/react'

import { initializePostHog } from '@/services/postHogService';

import "./styles/globals.css";
import './styles/fonts.css';
import './styles/dialog.css';
import { postHog } from "./config";

initializePostHog();

const options = {
  api_host: postHog.apiHost
}


ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <BrowserRouter>
      <PostHogProvider
        apiKey={postHog.apiKey}
        options={options}
      >
        <Provider store={store}>
          <PersistGate loading={null} persistor={persistor}>
            <ThemeProvider>
              <LoadingProvider>
                <AuthProvider>
                  <CreditsProvider>
                      <TabProvider>
                        <App />
                      </TabProvider>
                  </CreditsProvider>
                </AuthProvider>
              </LoadingProvider>
            </ThemeProvider>
          </PersistGate>
        </Provider>
      </PostHogProvider>
    </BrowserRouter>
  </React.StrictMode>,
);
