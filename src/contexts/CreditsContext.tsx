import { createContext, useContext, useEffect, useState, useRef } from "react";
import { agent<PERSON>pi, CreditsBalance } from "../services/agentApi";
import { useAuth } from "./AuthContext";
import { captureError, trackEvent } from "../services/postHogService";

interface CreditsContextType {
  credits: number;
  loading: boolean;
  error: string | null;
  tier: "free" | "pro" | "starter";
  refreshCredits: () => Promise<void>;
  creditResponse: CreditsBalance | null;
}

const CreditsContext = createContext<CreditsContextType>({
  credits: 0,
  loading: true,
  error: null,
  tier: "free",
  refreshCredits: async () => {},
  creditResponse: null,
});

export function CreditsProvider({ children }: { children: React.ReactNode }) {
  const [credits, setCredits] = useState<number>(0);
  const [creditResponse , setCreditResponse] = useState<CreditsBalance | null>(null);
  const [tier, setTier] = useState<"free"| "pro" |"starter">("free");
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const { user } = useAuth();

  const fetchCredits = async () => {
    if (!user) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const response = await agentApi.getCreditsBalance();
      // //console.log("Credits response:", response);
      if (response.error) {
        setError(response.error);
        setCredits(0);

        // Track credits API error response
        captureError(`Credits API returned error: ${response.error}`, {
          userId: user.id,
          errorType: 'api_response_error',
          apiResponse: response,
          context: 'credits_fetch'
        });

        trackEvent('credits_fetch_failed', {
          userId: user.id,
          errorType: 'api_response_error',
          errorMessage: response.error,
          hasInviteCode: !!user.user_metadata?.invite_code
        });
      } else {
        setCredits(response.ecu_balance || 0);
        setCreditResponse(response);
        setTier(response.subscription.name.includes("Pro") ? "pro" : response.subscription.name.includes("Starter") ? "starter" : "free");
        setError(null);
      }
    } catch (err : any) {
      console.error("Failed to fetch credits:", err);
      setError("Failed to fetch credits balance");
      setCredits(0);

      trackEvent('credits_fetch_failed', {
        userId: user.id,
        errorType: 'network_error',
        errorMessage: err ,
        hasInviteCode: !!user.user_metadata?.invite_code
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch credits when user changes
  useEffect(() => {
    if (user?.user_metadata.invite_code) {
      fetchCredits();
    }
  }, [user]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, []);

  const refreshCredits = async () => {
    // If a refresh is already in progress, ignore this call
    if (isRefreshing) {
      console.log("Credits refresh already in progress, ignoring this call");
      return;
    }

    if (!user?.user_metadata.invite_code) {
      console.log("User is not verified, not refreshing credits");
      return;
    }

    // Set the refreshing state to true
    setIsRefreshing(true);

    try {
      // Perform the actual refresh
      await fetchCredits();
    } finally {
      // Clear any existing timeout
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }

      // Set a timeout to clear the refreshing state after 5 seconds
      refreshTimeoutRef.current = setTimeout(() => {
        setIsRefreshing(false);
        refreshTimeoutRef.current = null;
      }, 5000); // 5 seconds cooldown
    }
  };

  const value = {
    credits,
    loading,
    tier,
    error,
    creditResponse,
    refreshCredits,
  };

  return <CreditsContext.Provider value={value}>{children}</CreditsContext.Provider>;
}

export const useCredits = () => {
  const context = useContext(CreditsContext);
  if (!context) {
    throw new Error("useCredits must be used within a CreditsProvider");
  }
  return context;
};
