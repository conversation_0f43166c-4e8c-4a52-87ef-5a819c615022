import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

const supabaseUrl = 'https://oakrnplafjvuupqxdokt.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9ha3JucGxhZmp2dXVwcXhkb2t0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTI0NTcyMzAsImV4cCI6MjAyODAzMzIzMH0.J3S0Yl39wX2LLfxDTpx4wyRThBW6zC-7Aq5Zs5GFOyM';

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});
