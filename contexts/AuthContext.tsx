import AsyncStorage from '@react-native-async-storage/async-storage';
import { Session, User } from '@supabase/supabase-js';
import * as AppleAuthentication from 'expo-apple-authentication';
import React, { createContext, useContext, useEffect, useState } from 'react';
import { Platform } from 'react-native';
import { supabase } from '../lib/supabase';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signUp: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signInWithApple: () => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
  storedCredentials: any;
  refreshStoredCredentials: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  session: null,
  loading: true,
  signIn: async () => ({ success: false }),
  signUp: async () => ({ success: false }),
  signInWithApple: async () => ({ success: false }),
  signOut: async () => {},
  storedCredentials: null,
  refreshStoredCredentials: async () => {},
});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [storedCredentials, setStoredCredentials] = useState<any>(null);

  // Store user credentials locally
  const storeCredentials = async (user: User, session: Session) => {
    try {
      const credentials = {
        user: {
          id: user.id,
          email: user.email,
          created_at: user.created_at,
          updated_at: user.updated_at,
          email_confirmed_at: user.email_confirmed_at,
          phone: user.phone,
          user_metadata: user.user_metadata,
          app_metadata: user.app_metadata,
        },
        session: {
          access_token: session.access_token,
          refresh_token: session.refresh_token,
          expires_at: session.expires_at,
          token_type: session.token_type,
        },
        stored_at: new Date().toISOString(),
      };

      await AsyncStorage.setItem('user_credentials', JSON.stringify(credentials));
      setStoredCredentials(credentials);
      console.log('User credentials stored locally:', credentials);
    } catch (error) {
      console.error('Error storing credentials:', error);
    }
  };

  // Refresh stored credentials from AsyncStorage
  const refreshStoredCredentials = async () => {
    try {
      const stored = await AsyncStorage.getItem('user_credentials');
      if (stored) {
        const credentials = JSON.parse(stored);
        setStoredCredentials(credentials);
        console.log('Loaded stored credentials:', credentials);
      }
    } catch (error) {
      console.error('Error loading stored credentials:', error);
    }
  };

  // Clear stored credentials
  const clearStoredCredentials = async () => {
    try {
      await AsyncStorage.removeItem('user_credentials');
      setStoredCredentials(null);
      console.log('Stored credentials cleared');
    } catch (error) {
      console.error('Error clearing stored credentials:', error);
    }
  };

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session: initialSession } } = await supabase.auth.getSession();

        if (initialSession) {
          setSession(initialSession);
          setUser(initialSession.user);
          await storeCredentials(initialSession.user, initialSession);
        }

        await refreshStoredCredentials();
      } catch (error) {
        console.error('Error getting initial session:', error);
      } finally {
        setLoading(false);
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session?.user?.email);

      if (session) {
        setSession(session);
        setUser(session.user);
        await storeCredentials(session.user, session);
      } else {
        setSession(null);
        setUser(null);
        await clearStoredCredentials();
      }

      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message || 'An error occurred during sign in' };
    }
  };

  const signUp = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message || 'An error occurred during sign up' };
    }
  };

  const signInWithApple = async () => {
    try {
      if (Platform.OS !== 'ios') {
        return { success: false, error: 'Apple Sign In is only available on iOS' };
      }

      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });

      if (credential.identityToken) {
        const { error, data: { user } } = await supabase.auth.signInWithIdToken({
          provider: 'apple',
          token: credential.identityToken,
        });

        console.log(JSON.stringify({ error, user }, null, 2));

        if (error) {
          return { success: false, error: error.message };
        }

        return { success: true };
      } else {
        return { success: false, error: 'No identity token received from Apple' };
      }
    } catch (error: any) {

      console.log(error, 'error');

      if (error.code === 'ERR_REQUEST_CANCELED') {
        return { success: false, error: 'Apple Sign In was cancelled' };
      }
      return { success: false, error: error.message || 'An error occurred during Apple sign in' };
    }
  };

  const signOut = async () => {
    try {
      await supabase.auth.signOut();
      await clearStoredCredentials();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const value = {
    user,
    session,
    loading,
    signIn,
    signUp,
    signInWithApple,
    signOut,
    storedCredentials,
    refreshStoredCredentials,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
